<?php
/**
 * Authentication and Security Functions
 * CYPTSHOP - Task 2.1.2.1.1: Create auth.php module
 */

// Prevent direct access
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(__DIR__) . '/');
}

// Config is included by the main application
require_once __DIR__ . '/database.php';
// require_once __DIR__ . '/database-connection.php'; // Removed to prevent function conflicts
require_once __DIR__ . '/db.php';

/**
 * Safe session start - prevents session errors
 */
function safeSessionStart() {
    if (session_status() === PHP_SESSION_NONE) {
        // Suppress any session warnings that might cause "ignore session" errors
        @session_start();
    }
}

/**
 * Hash password securely
 * @param string $password Plain text password
 * @return string Hashed password
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password against hash
 * @param string $password Plain text password
 * @param string $hash Stored password hash
 * @return bool Verification result
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Authenticate user login
 * @param string $username Username or email
 * @param string $password Plain text password
 * @return array|false User data on success, false on failure
 */
function authenticateUser($username, $password) {
    try {
        // Try to find user by username first
        $user = getTableRecord('users', ['username' => $username]);

        if (!$user) {
            // Try by email
            $user = getTableRecord('users', ['email' => $username]);
        }

        if ($user && verifyPassword($password, $user['password_hash'])) {
            // Update last login time
            updateTableData('users', ['last_login' => date('Y-m-d H:i:s')], ['id' => $user['id']]);

            // Log successful login
            logUserActivity($user['id'], 'login', 'user', $user['id'], [], ['ip' => $_SERVER['REMOTE_ADDR'] ?? '']);

            return $user;
        }

        // Log failed login attempt
        if ($user) {
            logUserActivity($user['id'], 'login_failed', 'user', $user['id'], [], ['ip' => $_SERVER['REMOTE_ADDR'] ?? '']);
        }

    } catch (Exception $e) {
        error_log('Authentication error: ' . $e->getMessage());
    }

    return false;
}

/**
 * Start secure session for authenticated user
 * @param array $user User data
 */
function startUserSession($user) {
    session_regenerate_id(true);
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_role'] = $user['role'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['logged_in'] = true;
    $_SESSION['login_time'] = time();
}

/**
 * Check if user is logged in
 * @return bool Login status
 */
function isLoggedIn() {
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

/**
 * Check if user has admin role
 * @return bool Admin status
 */
function isAdmin() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';
}

/**
 * Check if user has customer role
 * @return bool Customer status
 */
function isCustomer() {
    return isLoggedIn() && isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'customer';
}

/**
 * Get current user data
 * @return array|null User data or null if not logged in
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }

    try {
        return getTableRecord('users', ['id' => $_SESSION['user_id']]);
    } catch (Exception $e) {
        error_log('Error getting current user: ' . $e->getMessage());
        return null;
    }
}

/**
 * Logout user and destroy session
 */
function logoutUser() {
    session_unset();
    session_destroy();
    session_start();
}

/**
 * Require admin access (redirect if not admin)
 * @param string $redirectUrl URL to redirect non-admin users
 */
function requireAdmin($redirectUrl = '/admin/login/') {
    if (!isAdmin()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Require customer access (redirect if not customer)
 * @param string $redirectUrl URL to redirect non-customer users
 */
function requireCustomer($redirectUrl = '/account/login/') {
    if (!isCustomer()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Require any authenticated user
 * @param string $redirectUrl URL to redirect unauthenticated users
 */
function requireAuth($redirectUrl = '/account/login/') {
    if (!isLoggedIn()) {
        header("Location: $redirectUrl");
        exit;
    }
}

/**
 * Generate CSRF token
 * @return string CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * @param string $token Token to verify
 * @return bool Verification result
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Update user data
 * @param string $userId User ID
 * @param array $updateData Data to update
 * @return bool Success status
 */
function updateUserData($userId, $updateData) {
    try {
        return updateTableData('users', $updateData, ['id' => $userId]);
    } catch (Exception $e) {
        error_log('Error updating user data: ' . $e->getMessage());
        return false;
    }
}

/**
 * Register new user
 * @param array $userData User registration data
 * @return array|false User data on success, false on failure
 */
function registerUser($userData) {
    try {
        // Check if username or email already exists
        $existingUser = getTableRecord('users', ['username' => $userData['username']]);
        if ($existingUser) {
            return false;
        }

        $existingEmail = getTableRecord('users', ['email' => $userData['email']]);
        if ($existingEmail) {
            return false;
        }

        // Create new user
        $newUser = [
            'username' => $userData['username'],
            'email' => $userData['email'],
            'password_hash' => hashPassword($userData['password']),
            'first_name' => $userData['first_name'] ?? '',
            'last_name' => $userData['last_name'] ?? '',
            'role' => $userData['role'] ?? 'customer',
            'status' => 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'last_login' => null
        ];

        $userId = insertTableData('users', $newUser);

        if ($userId) {
            $newUser['id'] = $userId;

            // Log user registration
            logUserActivity($userId, 'register', 'user', $userId, [], ['ip' => $_SERVER['REMOTE_ADDR'] ?? '']);

            return $newUser;
        }

    } catch (Exception $e) {
        error_log('Error registering user: ' . $e->getMessage());
    }

    return false;
}

/**
 * Log user activity to admin_activity_log
 * @param int $userId User ID
 * @param string $action Action performed
 * @param string $entityType Entity type
 * @param int $entityId Entity ID
 * @param array $oldValues Old values (for updates)
 * @param array $metadata Additional metadata
 * @return bool Success status
 */
function logUserActivity($userId, $action, $entityType, $entityId, $oldValues = [], $metadata = []) {
    try {
        $logData = [
            'user_id' => $userId,
            'action' => $action,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'old_values' => !empty($oldValues) ? json_encode($oldValues) : null,
            'new_values' => null,
            'ip_address' => $metadata['ip'] ?? $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'session_id' => session_id(),
            'severity' => $metadata['severity'] ?? 'medium',
            'status' => 'success',
            'description' => $metadata['description'] ?? null,
            'metadata' => !empty($metadata) ? json_encode($metadata) : null,
            'created_at' => date('Y-m-d H:i:s')
        ];

        return insertTableData('admin_activity_log', $logData);

    } catch (Exception $e) {
        error_log('Error logging user activity: ' . $e->getMessage());
        return false;
    }
}
?>
