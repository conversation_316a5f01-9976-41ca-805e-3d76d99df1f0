<?php
/**
 * CYPTSHOP Simple Cart Sidebar
 * Working cart sidebar that matches JavaScript
 */
?>

<!-- Cart Sidebar Overlay -->
<div id="cartSidebarOverlay" class="cart-sidebar-overlay" onclick="closeCartSidebar()"></div>

<!-- Simple Cart Sidebar -->
<div id="cartSidebar" class="cart-sidebar">
    <!-- Car<PERSON> Header -->
    <div class="cart-sidebar-header bg-dark-grey-2 p-3 border-bottom border-cyan">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="text-cyan mb-0">
                <i class="fas fa-shopping-cart me-2"></i>
                Shopping Cart
                <span class="badge bg-magenta text-black ms-2" id="cartCountBadge">0</span>
            </h5>
            <button class="btn btn-sm btn-outline-cyan" onclick="closeCartSidebar()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>

    <!-- Cart Body -->
    <div class="cart-sidebar-body flex-grow-1 p-3">
        <!-- Empty Cart Message -->
        <div id="emptyCartMessage" class="text-center py-5">
            <i class="fas fa-shopping-cart fa-3x text-dark-grey-3 mb-3"></i>
            <h6 class="text-white mb-2">Your cart is empty</h6>
            <p class="text-off-white mb-3">Add some products to get started!</p>
            <button class="btn btn-cyan" onclick="closeCartSidebar()">
                <i class="fas fa-arrow-left me-1"></i>Continue Shopping
            </button>
        </div>

        <!-- Cart Items Container -->
        <div id="cartItems"></div>
    </div>

    <!-- Cart Footer -->
    <div class="cart-sidebar-footer bg-dark-grey-2 p-3 border-top border-cyan">
        <!-- Cart Summary -->
        <div class="cart-summary mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="cart-label">Subtotal:</span>
                <span class="cart-value" id="cartSubtotal">$0.00</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="cart-label">Shipping:</span>
                <span class="text-cyan" id="cartShipping">FREE</span>
            </div>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="cart-label">Tax:</span>
                <span class="cart-value" id="cartTax">$0.00</span>
            </div>
            <hr class="border-dark-grey-3 my-2">
            <div class="d-flex justify-content-between align-items-center">
                <span class="cart-total-label">Total:</span>
                <span class="text-yellow fw-bold h5 mb-0" id="cartTotal">$0.00</span>
            </div>
        </div>

        <!-- Promo Code Section -->
        <div class="promo-code-section mb-3">
            <div class="input-group input-group-sm">
                <input type="text"
                       class="form-control bg-dark-grey-1 border-dark-grey-3 text-white"
                       placeholder="Promo code"
                       id="promoCodeInput">
                <button class="btn btn-outline-cyan" type="button" onclick="applyPromoCode()">
                    Apply
                </button>
            </div>
        </div>

        <!-- Cart Actions -->
        <div class="cart-actions">
            <button class="btn btn-magenta w-100 mb-2" onclick="proceedToCheckout()">
                <i class="fas fa-credit-card me-2"></i>Secure Checkout
            </button>
            <div class="row g-2">
                <div class="col-6">
                    <a href="/cart/" class="btn btn-outline-cyan w-100">
                        <i class="fas fa-shopping-cart me-1"></i>View Cart
                    </a>
                </div>
                <div class="col-6">
                    <button class="btn btn-outline-secondary w-100" onclick="toggleSavedItems()">
                        <i class="fas fa-heart me-1"></i>Saved
                    </button>
                </div>
            </div>
            <button class="btn btn-outline-danger w-100 mt-2" onclick="clearCart()">
                <i class="fas fa-trash me-2"></i>Clear Cart
            </button>
        </div>

        <!-- Security Badge -->
        <div class="security-badge text-center mt-3">
            <small class="text-off-white">
                <i class="fas fa-lock me-1"></i>
                Secure SSL Checkout
            </small>
        </div>
    </div>
</div>

<!-- Enhanced Cart Sidebar CSS -->
<style>
/* Override existing cart sidebar styles with higher specificity */
#cartSidebarOverlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.7) !important;
    z-index: 9998 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    backdrop-filter: blur(4px) !important;
}

#cartSidebarOverlay.active {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

#cartSidebar {
    position: fixed !important;
    top: 0 !important;
    right: -420px !important;
    width: 420px !important;
    height: 100vh !important;
    background: #1a1a1a !important;
    border-left: 2px solid #00FFFF !important;
    z-index: 9999 !important;
    transition: right 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    display: flex !important;
    flex-direction: column !important;
    box-shadow: -5px 0 25px rgba(0, 255, 255, 0.3) !important;
    transform: none !important;
}

#cartSidebar.active {
    right: 0 !important;
    transform: none !important;
}

/* Industry-Standard Cart Item Styling */
.cart-item-card {
    background: rgba(26, 26, 26, 0.95) !important;
    border: 1px solid rgba(64, 64, 64, 0.5) !important;
    border-radius: 8px !important;
    margin-bottom: 16px !important;
    transition: all 0.2s ease !important;
    overflow: hidden !important;
}

.cart-item-card:hover {
    border-color: rgba(0, 255, 255, 0.4) !important;
    box-shadow: 0 2px 8px rgba(0, 255, 255, 0.1) !important;
}

.cart-item-content {
    display: flex !important;
    padding: 16px !important;
    gap: 16px !important;
    align-items: flex-start !important;
}

/* Product Image */
.cart-item-image-container {
    flex-shrink: 0 !important;
    width: 80px !important;
    height: 80px !important;
    border-radius: 6px !important;
    overflow: hidden !important;
    border: 1px solid rgba(0, 255, 255, 0.2) !important;
    background: rgba(0, 0, 0, 0.3) !important;
}

.cart-item-thumbnail {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: transform 0.2s ease !important;
}

.cart-item-card:hover .cart-item-thumbnail {
    transform: scale(1.05) !important;
}

/* Product Info */
.cart-item-info {
    flex: 1 !important;
    min-width: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 8px !important;
}

.cart-item-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    gap: 12px !important;
}

.cart-item-title {
    color: #ffffff !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    line-height: 1.3 !important;
    margin: 0 !important;
    flex: 1 !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
}

.cart-item-remove {
    background: none !important;
    border: none !important;
    color: rgba(255, 255, 255, 0.6) !important;
    font-size: 14px !important;
    padding: 4px !important;
    cursor: pointer !important;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
    flex-shrink: 0 !important;
}

.cart-item-remove:hover {
    color: #ff4757 !important;
    background: rgba(255, 71, 87, 0.1) !important;
}

.cart-item-details {
    display: flex !important;
    flex-direction: column !important;
    gap: 4px !important;
}

.cart-item-price {
    color: #00FFFF !important;
    font-size: 13px !important;
    font-weight: 500 !important;
}

.cart-item-meta {
    display: flex !important;
    gap: 12px !important;
}

.cart-item-sku {
    color: rgba(255, 255, 255, 0.5) !important;
    font-size: 11px !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

/* Quantity Controls & Total */
.cart-item-controls {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    gap: 16px !important;
    margin-top: 4px !important;
}

.quantity-section {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

.quantity-label {
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    margin: 0 !important;
    white-space: nowrap !important;
}

.quantity-controls {
    display: flex !important;
    align-items: center !important;
    background: rgba(0, 0, 0, 0.4) !important;
    border: 1px solid rgba(64, 64, 64, 0.6) !important;
    border-radius: 6px !important;
    overflow: hidden !important;
}

.quantity-btn {
    background: none !important;
    border: none !important;
    color: rgba(255, 255, 255, 0.8) !important;
    width: 28px !important;
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 11px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
}

.quantity-btn:hover:not(:disabled) {
    background: rgba(0, 255, 255, 0.15) !important;
    color: #00FFFF !important;
}

.quantity-btn:disabled {
    opacity: 0.4 !important;
    cursor: not-allowed !important;
}

.quantity-input {
    background: none !important;
    border: none !important;
    color: #ffffff !important;
    width: 40px !important;
    height: 28px !important;
    text-align: center !important;
    font-size: 13px !important;
    font-weight: 600 !important;
    outline: none !important;
}

.quantity-input:focus {
    background: rgba(0, 255, 255, 0.1) !important;
}

.cart-item-total-section {
    text-align: right !important;
}

.cart-item-total {
    color: #FFD700 !important;
    font-size: 16px !important;
    font-weight: 700 !important;
    line-height: 1 !important;
}

/* Action Buttons */
.cart-item-actions {
    margin-top: 8px !important;
}

.action-btn {
    background: none !important;
    border: 1px solid rgba(64, 64, 64, 0.6) !important;
    color: rgba(255, 255, 255, 0.7) !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    font-size: 11px !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 6px !important;
}

.action-btn:hover {
    border-color: rgba(0, 255, 255, 0.6) !important;
    color: #00FFFF !important;
    background: rgba(0, 255, 255, 0.05) !important;
}

.save-btn i {
    font-size: 10px !important;
}

/* Promo Code Section */
.promo-code-section .form-control {
    font-size: 13px !important;
    transition: all 0.2s ease !important;
}

.promo-code-section .form-control:focus {
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.3) !important;
    border-color: #00FFFF !important;
}

/* Security Badge */
.security-badge {
    opacity: 0.8 !important;
    transition: opacity 0.2s ease !important;
}

.security-badge:hover {
    opacity: 1 !important;
}

/* Scrollbar Styling */
.cart-sidebar-body::-webkit-scrollbar {
    width: 6px !important;
}

.cart-sidebar-body::-webkit-scrollbar-track {
    background: rgba(26, 26, 26, 0.5) !important;
}

.cart-sidebar-body::-webkit-scrollbar-thumb {
    background: rgba(0, 255, 255, 0.3) !important;
    border-radius: 3px !important;
}

.cart-sidebar-body::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 255, 255, 0.5) !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    #cartSidebar {
        width: 100% !important;
        right: -100% !important;
        max-width: 400px !important;
    }

    #cartSidebar.active {
        right: 0 !important;
    }

    .cart-item-content {
        padding: 12px !important;
        gap: 12px !important;
    }

    .cart-item-image-container {
        width: 70px !important;
        height: 70px !important;
    }

    .cart-item-title {
        font-size: 13px !important;
    }

    .cart-item-controls {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 8px !important;
    }

    .cart-item-total-section {
        text-align: left !important;
        align-self: flex-end !important;
    }

    .quantity-section {
        gap: 6px !important;
    }
}

@media (max-width: 480px) {
    #cartSidebar {
        width: 100vw !important;
        max-width: none !important;
    }

    .cart-item-content {
        flex-direction: column !important;
        align-items: center !important;
        text-align: center !important;
        padding: 16px !important;
    }

    .cart-item-image-container {
        width: 100px !important;
        height: 100px !important;
    }

    .cart-item-info {
        width: 100% !important;
        align-items: center !important;
    }

    .cart-item-header {
        flex-direction: column !important;
        align-items: center !important;
        gap: 8px !important;
    }

    .cart-item-controls {
        flex-direction: row !important;
        justify-content: space-between !important;
        width: 100% !important;
    }

    .cart-item-total-section {
        text-align: center !important;
    }
}

/* Cart Summary Text Colors - Enhanced for Dark Mode Readability */
.cart-label {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 14px !important;
    font-weight: 500 !important;
}

.cart-value {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600 !important;
}

.cart-total-label {
    color: #ffffff !important;
    font-weight: 700 !important;
    font-size: 16px !important;
}
</style>
