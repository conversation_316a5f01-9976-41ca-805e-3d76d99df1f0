/**
 * DTF Gang Builder - File Handling
 * File upload, processing, and management functionality
 */

// Extend the ProfessionalDTFBuilder class with file handling methods
Object.assign(ProfessionalDTFBuilder.prototype, {

    handleFiles(files) {
        console.log(`📁 Processing ${files.length} files...`);
        
        if (files.length === 0) return;
        
        // Show upload zone as active
        const uploadZone = document.getElementById('upload-zone');
        if (uploadZone) {
            uploadZone.classList.add('has-files');
        }
        
        // Process each file
        Array.from(files).forEach((file, index) => {
            this.processFile(file, index);
        });
        
        this.showStatus(`Processing ${files.length} file(s)...`, 'info');
    },

    processFile(file, index) {
        // Validate file type
        if (!this.isValidFileType(file)) {
            this.showStatus(`Invalid file type: ${file.name}`, 'error');
            return;
        }
        
        // Validate file size (50MB limit)
        const maxSize = 50 * 1024 * 1024; // 50MB
        if (file.size > maxSize) {
            this.showStatus(`File too large: ${file.name} (${this.formatFileSize(file.size)})`, 'error');
            return;
        }
        
        console.log(`📄 Processing file: ${file.name} (${this.formatFileSize(file.size)})`);
        
        // Create file reader
        const reader = new FileReader();
        
        reader.onload = (e) => {
            this.loadImageFromData(e.target.result, file.name, file.size);
        };
        
        reader.onerror = () => {
            this.showStatus(`Error reading file: ${file.name}`, 'error');
        };
        
        // Read file as data URL
        reader.readAsDataURL(file);
    },

    isValidFileType(file) {
        const validTypes = [
            'image/jpeg',
            'image/jpg', 
            'image/png',
            'image/gif',
            'image/bmp',
            'image/webp',
            'image/svg+xml',
            'application/pdf'
        ];
        
        return validTypes.includes(file.type.toLowerCase());
    },

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    loadImageFromData(dataUrl, fileName, fileSize) {
        const img = new Image();
        
        img.onload = () => {
            console.log(`🖼️ Image loaded: ${fileName} (${img.width}x${img.height})`);
            
            // Calculate DPI and physical dimensions
            const imageInfo = this.calculateImageInfo(img, fileName, fileSize);
            
            // Add to images array
            this.images.push({
                image: img,
                name: fileName,
                size: fileSize,
                width: img.width,
                height: img.height,
                ...imageInfo
            });
            
            // Update UI
            this.updateImageList();
            this.updateStats();
            this.updateButtonStates();
            
            // Auto-place image on canvas
            this.autoPlaceImage(img, imageInfo);
            
            this.showStatus(`Added: ${fileName}`, 'success');
        };
        
        img.onerror = () => {
            this.showStatus(`Failed to load image: ${fileName}`, 'error');
        };
        
        img.src = dataUrl;
    },

    calculateImageInfo(img, fileName, fileSize) {
        // Estimate DPI (assume 300 DPI if not specified)
        const assumedDPI = 300;
        
        // Calculate physical dimensions in inches
        const physicalWidth = img.width / assumedDPI;
        const physicalHeight = img.height / assumedDPI;
        
        // Calculate how many copies can fit on current sheet
        const dims = this.getSheetDimensions();
        const maxCopiesX = Math.floor(dims.realWidth / physicalWidth);
        const maxCopiesY = Math.floor(dims.realHeight / physicalHeight);
        const maxCopies = maxCopiesX * maxCopiesY;
        
        return {
            dpi: assumedDPI,
            physicalWidth: physicalWidth,
            physicalHeight: physicalHeight,
            maxCopies: Math.max(1, maxCopies)
        };
    },

    updateImageList() {
        const imageList = document.getElementById('image-list');
        if (!imageList) return;
        
        if (this.images.length === 0) {
            imageList.classList.add('hidden');
            return;
        }
        
        imageList.classList.remove('hidden');
        
        // Generate image list HTML
        imageList.innerHTML = this.images.map((img, index) => `
            <div class="image-item" data-index="${index}">
                <canvas class="image-thumb" width="30" height="30"></canvas>
                <div class="image-info">
                    <div class="image-name">${img.name}</div>
                    <div class="image-size">${img.width} × ${img.height} px</div>
                    <div class="image-dpi">${img.dpi} DPI</div>
                    <div class="image-physical">${img.physicalWidth.toFixed(2)}" × ${img.physicalHeight.toFixed(2)}"</div>
                    <div class="max-copies">Max: ${img.maxCopies} copies</div>
                </div>
                <input type="number" class="quantity-input" value="1" min="1" max="${img.maxCopies}" 
                       onchange="window.dtfBuilder.updateImageQuantity(${index}, this.value)">
            </div>
        `).join('');
        
        // Draw thumbnails
        this.images.forEach((img, index) => {
            this.drawThumbnail(img.image, index);
        });
    },

    drawThumbnail(image, index) {
        const thumbnail = document.querySelector(`[data-index="${index}"] .image-thumb`);
        if (!thumbnail) return;
        
        const ctx = thumbnail.getContext('2d');
        const size = 30;
        
        // Calculate aspect ratio
        const aspectRatio = image.width / image.height;
        let drawWidth, drawHeight;
        
        if (aspectRatio > 1) {
            drawWidth = size;
            drawHeight = size / aspectRatio;
        } else {
            drawWidth = size * aspectRatio;
            drawHeight = size;
        }
        
        // Center the image
        const x = (size - drawWidth) / 2;
        const y = (size - drawHeight) / 2;
        
        // Clear and draw
        ctx.clearRect(0, 0, size, size);
        ctx.drawImage(image, x, y, drawWidth, drawHeight);
    },

    updateImageQuantity(imageIndex, quantity) {
        const qty = parseInt(quantity);
        if (isNaN(qty) || qty < 1) return;
        
        const img = this.images[imageIndex];
        if (!img) return;
        
        // Remove existing positions for this image
        this.imagePositions = this.imagePositions.filter(pos => pos.imageIndex !== imageIndex);
        
        // Add new positions
        for (let i = 0; i < qty; i++) {
            this.autoPlaceImage(img.image, img, imageIndex);
        }
        
        this.updateStats();
        this.drawWithImages();
        
        console.log(`🔢 Updated quantity for ${img.name}: ${qty} copies`);
    },

    autoPlaceImage(image, imageInfo, imageIndex = null) {
        const dims = this.getSheetDimensions();
        
        // Calculate display size (scale down for canvas)
        const displayWidth = imageInfo.physicalWidth * dims.scale;
        const displayHeight = imageInfo.physicalHeight * dims.scale;
        
        // Find a good position (simple grid placement for now)
        let x = this.spacing * dims.scale;
        let y = this.spacing * dims.scale;
        
        // Try to find an empty spot
        let placed = false;
        const gridSize = Math.max(displayWidth, displayHeight) + (this.spacing * dims.scale);
        
        for (let row = 0; row < 10 && !placed; row++) {
            for (let col = 0; col < 10 && !placed; col++) {
                const testX = col * gridSize + (this.spacing * dims.scale);
                const testY = row * gridSize + (this.spacing * dims.scale);
                
                // Check if position is within bounds
                if (testX + displayWidth <= dims.width - (this.spacing * dims.scale) &&
                    testY + displayHeight <= dims.height - (this.spacing * dims.scale)) {
                    
                    // Check if position is free
                    const overlaps = this.imagePositions.some(pos => 
                        this.rectanglesOverlap(
                            testX, testY, displayWidth, displayHeight,
                            pos.x, pos.y, pos.width, pos.height
                        )
                    );
                    
                    if (!overlaps) {
                        x = testX;
                        y = testY;
                        placed = true;
                    }
                }
            }
        }
        
        // Add to positions
        this.imagePositions.push({
            image: image,
            x: x,
            y: y,
            width: displayWidth,
            height: displayHeight,
            imageIndex: imageIndex !== null ? imageIndex : this.images.length - 1,
            rotation: 0
        });
        
        this.drawWithImages();
    },

    rectanglesOverlap(x1, y1, w1, h1, x2, y2, w2, h2) {
        return !(x1 + w1 <= x2 || x2 + w2 <= x1 || y1 + h1 <= y2 || y2 + h2 <= y1);
    },

    applyQuantityToAll() {
        const defaultQuantitySelect = document.getElementById('default-quantity');
        const customQuantityInput = document.getElementById('custom-quantity-input');
        
        if (!defaultQuantitySelect) return;
        
        let quantity;
        if (defaultQuantitySelect.value === 'custom') {
            quantity = parseInt(customQuantityInput.value) || 1;
        } else {
            quantity = parseInt(defaultQuantitySelect.value) || 1;
        }
        
        // Clear all current positions
        this.imagePositions = [];
        
        // Apply quantity to all images
        this.images.forEach((img, index) => {
            const maxQty = Math.min(quantity, img.maxCopies);
            for (let i = 0; i < maxQty; i++) {
                this.autoPlaceImage(img.image, img, index);
            }
            
            // Update quantity input in UI
            const quantityInput = document.querySelector(`[data-index="${index}"] .quantity-input`);
            if (quantityInput) {
                quantityInput.value = maxQty;
            }
        });
        
        this.updateStats();
        this.drawWithImages();
        
        this.showStatus(`Applied quantity ${quantity} to all images`, 'success');
    },

    // Layout and optimization methods
    autoNest() {
        console.log('🧩 Auto-nesting images...');
        this.showStatus('Auto-nesting images...', 'info');

        // Simple auto-nesting algorithm
        this.imagePositions = [];
        const dims = this.getSheetDimensions();

        let currentX = this.spacing * dims.scale;
        let currentY = this.spacing * dims.scale;
        let rowHeight = 0;

        this.images.forEach((img, imgIndex) => {
            const quantity = parseInt(document.querySelector(`[data-index="${imgIndex}"] .quantity-input`)?.value) || 1;

            for (let i = 0; i < quantity; i++) {
                const displayWidth = img.physicalWidth * dims.scale;
                const displayHeight = img.physicalHeight * dims.scale;

                // Check if we need to move to next row
                if (currentX + displayWidth > dims.width - (this.spacing * dims.scale)) {
                    currentX = this.spacing * dims.scale;
                    currentY += rowHeight + (this.spacing * dims.scale);
                    rowHeight = 0;
                }

                // Check if we have vertical space
                if (currentY + displayHeight <= dims.height - (this.spacing * dims.scale)) {
                    this.imagePositions.push({
                        image: img.image,
                        x: currentX,
                        y: currentY,
                        width: displayWidth,
                        height: displayHeight,
                        imageIndex: imgIndex,
                        rotation: 0
                    });

                    currentX += displayWidth + (this.spacing * dims.scale);
                    rowHeight = Math.max(rowHeight, displayHeight);
                }
            }
        });

        this.updateStats();
        this.drawWithImages();
        this.showStatus('Auto-nesting completed!', 'success');
    },

    optimizeLayout() {
        console.log('⚡ Optimizing layout...');
        this.showStatus('Optimizing layout...', 'info');

        // Simple optimization: sort by size and repack
        const sortedPositions = [...this.imagePositions].sort((a, b) => {
            return (b.width * b.height) - (a.width * a.height);
        });

        this.imagePositions = [];
        const dims = this.getSheetDimensions();

        sortedPositions.forEach(pos => {
            // Find best position for this image
            let bestX = this.spacing * dims.scale;
            let bestY = this.spacing * dims.scale;
            let placed = false;

            for (let y = this.spacing * dims.scale; y <= dims.height - pos.height - (this.spacing * dims.scale) && !placed; y += 10) {
                for (let x = this.spacing * dims.scale; x <= dims.width - pos.width - (this.spacing * dims.scale) && !placed; x += 10) {
                    const overlaps = this.imagePositions.some(existing =>
                        this.rectanglesOverlap(x, y, pos.width, pos.height, existing.x, existing.y, existing.width, existing.height)
                    );

                    if (!overlaps) {
                        bestX = x;
                        bestY = y;
                        placed = true;
                    }
                }
            }

            this.imagePositions.push({
                ...pos,
                x: bestX,
                y: bestY
            });
        });

        this.updateStats();
        this.drawWithImages();
        this.showStatus('Layout optimized!', 'success');
    },

    fillEntireSheet() {
        console.log('📐 Filling entire sheet...');
        this.showStatus('Filling entire sheet...', 'info');

        if (this.images.length === 0) {
            this.showStatus('No images to fill with!', 'error');
            return;
        }

        // Use the first image to fill the sheet
        const img = this.images[0];
        const dims = this.getSheetDimensions();

        const displayWidth = img.physicalWidth * dims.scale;
        const displayHeight = img.physicalHeight * dims.scale;

        // Calculate how many fit
        const cols = Math.floor((dims.width - this.spacing * dims.scale) / (displayWidth + this.spacing * dims.scale));
        const rows = Math.floor((dims.height - this.spacing * dims.scale) / (displayHeight + this.spacing * dims.scale));

        this.imagePositions = [];

        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const x = col * (displayWidth + this.spacing * dims.scale) + this.spacing * dims.scale;
                const y = row * (displayHeight + this.spacing * dims.scale) + this.spacing * dims.scale;

                this.imagePositions.push({
                    image: img.image,
                    x: x,
                    y: y,
                    width: displayWidth,
                    height: displayHeight,
                    imageIndex: 0,
                    rotation: 0
                });
            }
        }

        this.updateStats();
        this.drawWithImages();
        this.showStatus(`Filled sheet with ${this.imagePositions.length} copies!`, 'success');
    },

    downloadGangSheet() {
        console.log('📥 Downloading gang sheet...');

        // Create a temporary canvas for export
        const exportCanvas = document.createElement('canvas');
        const exportCtx = exportCanvas.getContext('2d');
        const dims = this.getSheetDimensions();

        exportCanvas.width = dims.width;
        exportCanvas.height = dims.height;

        // Fill with white background
        exportCtx.fillStyle = 'white';
        exportCtx.fillRect(0, 0, dims.width, dims.height);

        // Draw all images (without grid)
        this.imagePositions.forEach(pos => {
            if (pos.image && pos.image.complete) {
                exportCtx.drawImage(pos.image, pos.x, pos.y, pos.width, pos.height);
            }
        });

        // Download
        const format = document.getElementById('export-format').value;
        const mimeType = format === 'jpg' ? 'image/jpeg' : `image/${format}`;

        exportCanvas.toBlob((blob) => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `dtf-gang-sheet-${Date.now()}.${format}`;
            a.click();
            URL.revokeObjectURL(url);

            this.showStatus(`Downloaded as ${format.toUpperCase()}!`, 'success');
        }, mimeType, 0.9);
    },

    generatePDF() {
        console.log('🖨️ Generating PDF...');
        this.showStatus('PDF generation not implemented in demo', 'info');
    },

    saveProject() {
        console.log('💾 Saving project...');
        this.showStatus('Project saving not implemented in demo', 'info');
    },

    loadProject() {
        console.log('📂 Loading project...');
        this.showStatus('Project loading not implemented in demo', 'info');
    },

    createOrder() {
        console.log('🛒 Creating order...');
        this.showStatus('Order creation not implemented in demo', 'info');
    },

    updateTotalPrice() {
        const unitPrice = parseFloat(document.getElementById('unit-price').value) || 0;
        const quantity = parseInt(document.getElementById('order-quantity').value) || 1;
        const total = unitPrice * quantity;

        document.getElementById('total-price').textContent = `$${total.toFixed(2)}`;
    }

});
