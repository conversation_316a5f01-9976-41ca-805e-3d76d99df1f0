/* DTF Gang Builder - Component Styles */

/* Accordion Menu System */
.accordion-item {
    border-bottom: 1px solid #eee;
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    padding: 15px 20px;
    background: #f8f9fa;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    color: #2c3e50;
    transition: all 0.3s ease;
    border: none;
    width: 100%;
    text-align: left;
}

.accordion-header:hover {
    background: #e9ecef;
}

.accordion-header.active {
    background: #3498db;
    color: white;
}

.accordion-icon {
    transition: transform 0.3s ease;
    font-size: 0.8rem;
}

.accordion-header.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: white;
}

.accordion-content.active {
    max-height: 1000px;
}

.accordion-body {
    padding: 20px;
}

/* Control Sections */
.control-section {
    margin-bottom: 20px;
}

.control-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.control-group {
    margin-bottom: 15px;
}

.control-label {
    display: block;
    font-weight: 500;
    margin-bottom: 5px;
    color: #555;
    font-size: 0.9rem;
}

.control-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

.control-input:focus {
    outline: none;
    border-color: #3498db;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

.checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px;
}

/* Upload Zone */
.upload-zone {
    border: 2px dashed #bdc3c7;
    border-radius: 8px;
    padding: 30px 15px;
    text-align: center;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 15px;
}

.upload-zone:hover {
    border-color: #3498db;
    background: #ecf0f1;
}

.upload-zone.has-files {
    border-color: #27ae60;
    background: #d5f4e6;
}

/* Image List */
.image-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 5px;
    padding: 10px;
}

.image-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 0.85rem;
}

.image-item:last-child {
    border-bottom: none;
}

.image-thumb {
    width: 30px;
    height: 30px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 10px;
}

.image-info {
    flex: 1;
}

.image-name {
    font-weight: 500;
    color: #2c3e50;
}

.image-size {
    color: #7f8c8d;
    font-size: 0.75rem;
}

.image-dpi {
    color: #3498db;
    font-size: 0.7rem;
    font-weight: 600;
    margin-top: 1px;
}

.image-physical {
    color: #e67e22;
    font-size: 0.7rem;
    font-weight: 600;
    margin-top: 1px;
}

.max-copies {
    color: #27ae60;
    font-size: 0.7rem;
    font-weight: 600;
    margin-top: 2px;
}

.quantity-input {
    width: 50px;
    padding: 4px;
    border: 1px solid #ddd;
    border-radius: 3px;
    text-align: center;
    font-size: 0.8rem;
}

/* Enhanced form styling */
textarea.control-input {
    resize: vertical;
    min-height: 60px;
    font-family: inherit;
}

.control-input[type="email"]:invalid {
    border-color: #e74c3c;
}

.control-input[type="email"]:valid {
    border-color: #27ae60;
}

/* Grid controls styling */
.grid-controls {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
}

/* Grid overlay indicator */
.grid-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 1px solid #3498db;
    margin-right: 5px;
    vertical-align: middle;
}

.grid-indicator.active {
    background: #3498db;
}
