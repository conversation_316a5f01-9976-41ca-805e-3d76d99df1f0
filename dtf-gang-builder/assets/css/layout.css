/* DTF Gang Builder - Layout Styles */

.main-grid {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 15px;
    height: calc(100vh - 120px);
    overflow: hidden;
}

.sidebar {
    background: white;
    border-radius: 10px;
    padding: 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.canvas-area {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.stat-card {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    border: 1px solid #e9ecef;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
}

.stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
}

/* Canvas Toolbar */
.canvas-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 15px;
}

.canvas-info-header h3 {
    margin: 0;
    font-size: 1.1rem;
    color: #2c3e50;
}

.sheet-info {
    font-size: 0.85rem;
    color: #7f8c8d;
}

.canvas-tools {
    display: flex;
    gap: 8px;
}

.tool-separator {
    width: 1px;
    height: 24px;
    background: #dee2e6;
    margin: 0 8px;
}

.zoom-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: #666;
}

.zoom-level {
    font-weight: 600;
    color: #2c3e50;
    min-width: 50px;
    text-align: center;
}

/* Canvas Container */
.canvas-container {
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
    position: relative;
    overflow: hidden;
    margin-bottom: 15px;
    user-select: none;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
}

.canvas-container.zoomed {
    overflow: auto;
    cursor: grab;
}

.canvas-container.panning {
    cursor: grabbing;
}

#gang-canvas {
    display: block;
    max-width: 100%;
    background: white;
}

.canvas-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-top: 1px solid #eee;
    font-size: 0.85rem;
    color: #666;
}

/* Responsive Layout */
@media (max-width: 1200px) {
    .main-grid {
        grid-template-columns: 300px 1fr;
        gap: 10px;
    }
}

@media (max-width: 992px) {
    .main-grid {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .sidebar {
        height: auto;
        margin-bottom: 15px;
    }
    
    .canvas-area {
        height: 70vh;
    }
}

@media (max-width: 768px) {
    .canvas-toolbar {
        flex-direction: column;
        gap: 10px;
        padding: 10px;
    }
    
    .canvas-tools {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 5px;
    }
}
