<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Professional DTF Gang Sheet Builder</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Modular CSS Files -->
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/canvas.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Professional DTF Gang Sheet Builder</h1>
            <p>Industry-standard features for professional DTF printing</p>
        </div>

        <div class="main-grid">
            <!-- Professional Accordion Sidebar -->
            <div class="sidebar">
                <!-- File Management Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header active" onclick="toggleAccordion(this)">
                        <span>📁 File Management</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content active">
                        <div class="accordion-body">
                            <div id="upload-zone" class="upload-zone">
                                <div>📤 Drop files or click to upload</div>
                                <small>PNG, JPG, PDF, AI, EPS • Max 50MB each</small>
                            </div>
                            <input type="file" id="file-input" multiple accept="image/*,.pdf,.ai,.eps" style="display: none;">
                            <div id="image-list" class="image-list hidden"></div>
                        </div>
                    </div>
                </div>

                <!-- Sheet Configuration Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>📐 Sheet Configuration</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <div class="control-group">
                                <label class="control-label">Sheet Size</label>
                                <select id="sheet-size" class="control-input">
                                    <option value="30x12">30" × 12" (Standard)</option>
                                    <option value="30x24">30" × 24"</option>
                                    <option value="30x36">30" × 36"</option>
                                    <option value="30x48">30" × 48"</option>
                                    <option value="30x60">30" × 60"</option>
                                    <option value="30x72" selected>30" × 72" (Popular)</option>
                                    <option value="30x100">30" × 100"</option>
                                    <option value="30x120">30" × 120" (Max)</option>
                                </select>
                            </div>

                            <div class="settings-grid">
                                <div class="control-group">
                                    <label class="control-label">DPI</label>
                                    <select id="dpi" class="control-input">
                                        <option value="150">150 DPI</option>
                                        <option value="300" selected>300 DPI</option>
                                        <option value="600">600 DPI</option>
                                    </select>
                                </div>
                                <div class="control-group">
                                    <label class="control-label">Color Mode</label>
                                    <select id="color-mode" class="control-input">
                                        <option value="cmyk" selected>CMYK</option>
                                        <option value="rgb">RGB</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Settings Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>⚙️ Advanced Settings</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <!-- Auto-Nesting Settings -->
                            <div class="control-section">
                                <div class="section-title">🧩 Auto-Nesting Settings</div>
                                <div class="settings-grid">
                                    <div class="control-group">
                                        <label class="control-label">Spacing (inches)</label>
                                        <input type="number" id="spacing" class="control-input" value="0.125" min="0" max="2" step="0.125">
                                    </div>
                                    <div class="control-group">
                                        <label class="control-label">Bleed (inches)</label>
                                        <input type="number" id="bleed" class="control-input" value="0.0625" min="0" max="0.5" step="0.0625">
                                    </div>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="auto-rotate" checked>
                                    <label for="auto-rotate">Auto-rotate for optimal fit</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="maintain-aspect" checked>
                                    <label for="maintain-aspect">Maintain aspect ratio</label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="add-margins" checked>
                                    <label for="add-margins">Add safety margins</label>
                                </div>
                            </div>

                            <!-- Grid Settings -->
                            <div class="control-section">
                                <div class="section-title">📐 Precision Grid</div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="show-grid" checked>
                                    <label for="show-grid">
                                        <span class="grid-indicator active"></span>Show grid overlay
                                    </label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="snap-to-grid" checked>
                                    <label for="snap-to-grid">
                                        🧲 Snap to grid
                                    </label>
                                </div>

                                <div class="checkbox-group">
                                    <input type="checkbox" id="show-grid-numbers">
                                    <label for="show-grid-numbers">
                                        🔢 Show grid numbers
                                    </label>
                                </div>

                                <div class="settings-grid">
                                    <div class="control-group">
                                        <label class="control-label">Grid Size</label>
                                        <select id="grid-size" class="control-input">
                                            <option value="0.25">1/4 inch (0.25")</option>
                                            <option value="0.5">1/2 inch (0.5")</option>
                                            <option value="1" selected>1 inch (1.0")</option>
                                            <option value="2">2 inches (2.0")</option>
                                        </select>
                                    </div>
                                    <div class="control-group">
                                        <label class="control-label">Grid Color</label>
                                        <select id="grid-color" class="control-input">
                                            <option value="light" selected>Light Gray</option>
                                            <option value="medium">Medium Gray</option>
                                            <option value="dark">Dark Gray</option>
                                            <option value="blue">Blue</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Quantity Controls -->
                            <div class="control-section">
                                <div class="section-title">🔢 Quantity Settings</div>

                                <div class="control-group">
                                    <label class="control-label">Default Quantity per Image</label>
                                    <select id="default-quantity" class="control-input">
                                        <option value="1">1 copy</option>
                                        <option value="2">2 copies</option>
                                        <option value="5">5 copies</option>
                                        <option value="10">10 copies</option>
                                        <option value="25">25 copies</option>
                                        <option value="50">50 copies</option>
                                        <option value="100">100 copies</option>
                                        <option value="custom">Custom quantity...</option>
                                    </select>
                                </div>

                                <div class="control-group hidden" id="custom-quantity-group">
                                    <label class="control-label">Custom Quantity</label>
                                    <input type="number" id="custom-quantity-input" class="control-input" value="1" min="1" max="500">
                                </div>

                                <button id="apply-quantity-btn" class="btn btn-primary" disabled>
                                    🔢 Apply to All Images
                                </button>
                            </div>

                            <!-- Actions -->
                            <div class="control-section">
                                <div class="section-title">⚡ Actions</div>
                                <button id="auto-nest-btn" class="btn btn-primary" disabled>
                                    🧩 Auto-Nest Images
                                </button>
                                <button id="optimize-btn" class="btn btn-primary" disabled>
                                    ⚡ Optimize Layout
                                </button>
                                <button id="fill-sheet-btn" class="btn btn-success" disabled>
                                    📐 Fill Entire Sheet
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Accordion -->
                <div class="accordion-item">
                    <button class="accordion-header" onclick="toggleAccordion(this)">
                        <span>⚡ Quick Actions</span>
                        <span class="accordion-icon">▼</span>
                    </button>
                    <div class="accordion-content">
                        <div class="accordion-body">
                            <div class="control-group">
                                <label class="control-label">Export Format</label>
                                <select id="export-format" class="control-input">
                                    <option value="png" selected>PNG (Preview)</option>
                                    <option value="jpg">JPEG (Compressed)</option>
                                    <option value="tiff">TIFF (Professional)</option>
                                </select>
                            </div>

                            <button id="download-btn" class="btn btn-primary" disabled>
                                📥 Download Preview
                            </button>

                            <button id="generate-pdf-btn" class="btn btn-success" disabled>
                                🖨️ Generate Print PDF
                            </button>

                            <div id="pdf-progress-container" class="progress-container hidden">
                                <div class="progress-header">
                                    <span id="pdf-progress-text">Generating PDF...</span>
                                    <span id="pdf-progress-percent">0%</span>
                                </div>
                                <div class="progress-bar">
                                    <div id="pdf-progress-fill" class="progress-fill"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-area">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value" id="total-images">0</div>
                        <div class="stat-label">Images</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="efficiency">0%</div>
                        <div class="stat-label">Efficiency</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" id="total-copies">0</div>
                        <div class="stat-label">Total Copies</div>
                    </div>
                </div>

                <!-- Canvas Toolbar with Zoom Controls -->
                <div class="canvas-toolbar">
                    <div class="canvas-info-header">
                        <h3>Professional DTF Gang Sheet Canvas</h3>
                        <span class="sheet-info">30" Wide DTF Machine Compatible</span>
                    </div>

                    <div class="canvas-tools">
                        <button id="zoom-in" class="tool-button" title="Zoom In (Ctrl/Cmd + Plus)">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button id="zoom-out" class="tool-button" title="Zoom Out (Ctrl/Cmd + Minus)">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button id="zoom-fit" class="tool-button" title="Fit to View">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button id="zoom-reset" class="tool-button" title="Reset Zoom (Ctrl/Cmd + 0)">
                            <i class="fas fa-compress"></i>
                        </button>
                        <div class="tool-separator"></div>
                        <button id="select-tool" class="tool-button active" title="Selection Tool (V) - Select and move objects">
                            <i class="fas fa-mouse-pointer"></i>
                        </button>
                        <button id="hand-tool" class="tool-button" title="Pan Tool (H) - Click and drag to pan view">
                            <i class="fas fa-hand-paper"></i>
                        </button>
                    </div>

                    <div class="zoom-controls">
                        <span>Zoom:</span>
                        <span id="zoom-level" class="zoom-level">100%</span>
                    </div>
                </div>

                <div class="canvas-container">
                    <canvas id="gang-canvas" width="1000" height="600"></canvas>
                    <div class="canvas-info">
                        <span>Sheet: <span id="sheet-dimensions">30" × 72"</span></span>
                        <span>Scale: <span id="canvas-scale">1:10</span></span>
                        <span>Print Size: <span id="print-size">30" × 72"</span></span>
                    </div>
                </div>

                <div id="status" class="status hidden"></div>
            </div>
        </div>
    </div>

    <!-- Modular JavaScript Files -->
    <script src="assets/js/dtf-builder-core.js"></script>
    <script src="assets/js/dtf-builder-events.js"></script>
    <script src="assets/js/dtf-builder-tools.js"></script>
    <script src="assets/js/dtf-builder-files.js"></script>
    
    <script>
        // Accordion Toggle Function
        function toggleAccordion(header) {
            const content = header.nextElementSibling;
            const icon = header.querySelector('.accordion-icon');

            // Close all other accordions
            document.querySelectorAll('.accordion-header').forEach(h => {
                if (h !== header) {
                    h.classList.remove('active');
                    h.nextElementSibling.classList.remove('active');
                }
            });

            // Toggle current accordion
            header.classList.toggle('active');
            content.classList.toggle('active');
        }

        // Initialize the DTF Builder when page loads
        let dtfBuilder;
        
        document.addEventListener('DOMContentLoaded', function() {
            try {
                console.log('🚀 Initializing DTF Gang Builder...');
                dtfBuilder = new ProfessionalDTFBuilder();
                window.dtfBuilder = dtfBuilder; // Make globally accessible
                console.log('✅ DTF Gang Builder initialized successfully!');
            } catch (error) {
                console.error('❌ Failed to initialize DTF Builder:', error);
                document.getElementById('status').innerHTML = `
                    <div class="status error">
                        Failed to initialize: ${error.message}
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
