<?php
/**
 * Admin Analytics Dashboard
 * CYPTSHOP - Advanced Analytics and Reporting
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Get data for analytics (using database or mock data for now)
$orders = []; // Will be populated from database
$products = []; // Will be populated from database
$users = []; // Will be populated from database
$contacts = []; // Will be populated from database

// Mock data for demonstration
$orders = [
    ['id' => 1, 'total' => 150.00, 'status' => 'completed', 'customer_name' => '<PERSON>', 'created_at' => '2024-01-15', 'items' => [['product' => ['id' => 1, 'name' => 'T-Shirt'], 'quantity' => 2, 'total' => 150.00]]],
    ['id' => 2, 'total' => 75.50, 'status' => 'pending', 'customer_name' => '<PERSON>', 'created_at' => '2024-01-20', 'items' => [['product' => ['id' => 2, 'name' => 'Hoodie'], 'quantity' => 1, 'total' => 75.50]]],
    ['id' => 3, 'total' => 200.00, 'status' => 'shipped', 'customer_name' => 'Bob Johnson', 'created_at' => '2024-02-01', 'items' => [['product' => ['id' => 1, 'name' => 'T-Shirt'], 'quantity' => 4, 'total' => 200.00]]]
];

$products = [
    ['id' => 1, 'name' => 'CYPTSHOP T-Shirt', 'price' => 25.00],
    ['id' => 2, 'name' => 'CYPTSHOP Hoodie', 'price' => 75.50],
    ['id' => 3, 'name' => 'CYPTSHOP Cap', 'price' => 15.00]
];

$users = [
    ['id' => 1, 'name' => 'John Doe', 'role' => 'customer'],
    ['id' => 2, 'name' => 'Jane Smith', 'role' => 'customer'],
    ['id' => 3, 'name' => 'Admin User', 'role' => 'admin']
];

// Calculate analytics
$totalRevenue = array_sum(array_column($orders, 'total'));
$totalOrders = count($orders);
$totalCustomers = count(array_filter($users, function($user) { return $user['role'] === 'customer'; }));
$totalProducts = count($products);

// Monthly revenue calculation
$monthlyRevenue = [];
$currentYear = date('Y');
for ($month = 1; $month <= 12; $month++) {
    $monthlyRevenue[$month] = 0;
}

foreach ($orders as $order) {
    $orderDate = strtotime($order['created_at']);
    if (date('Y', $orderDate) == $currentYear) {
        $month = (int)date('n', $orderDate);
        $monthlyRevenue[$month] += $order['total'];
    }
}

// Top selling products
$productSales = [];
foreach ($orders as $order) {
    foreach ($order['items'] as $item) {
        $productId = $item['product']['id'];
        if (!isset($productSales[$productId])) {
            $productSales[$productId] = [
                'product' => $item['product'],
                'quantity' => 0,
                'revenue' => 0
            ];
        }
        $productSales[$productId]['quantity'] += $item['quantity'];
        $productSales[$productId]['revenue'] += $item['total'];
    }
}

// Sort by quantity sold
uasort($productSales, function($a, $b) {
    return $b['quantity'] - $a['quantity'];
});

$topProducts = array_slice($productSales, 0, 5, true);

// Recent activity
$recentOrders = array_slice(array_reverse($orders), 0, 10);
$recentUsers = array_slice(array_filter($users, function($user) { 
    return $user['role'] === 'customer'; 
}), -5);

// Order status distribution
$statusCounts = [];
foreach ($orders as $order) {
    $status = $order['status'];
    $statusCounts[$status] = ($statusCounts[$status] ?? 0) + 1;
}

$pageTitle = 'Analytics Dashboard - Admin';
$bodyClass = 'admin-analytics';

include BASE_PATH . 'includes/header.php';
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Analytics Dashboard</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-outline-cyan" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print Report
                    </button>
                </div>
            </div>

            <!-- Key Metrics -->
            <div class="row g-4 mb-5">
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-dollar-sign fa-3x text-cyan mb-3"></i>
                            <h3 class="text-cyan">$<?php echo number_format($totalRevenue, 2); ?></h3>
                            <p class="text-off-white mb-0">Total Revenue</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body text-center">
                            <i class="fas fa-shopping-cart fa-3x text-magenta mb-3"></i>
                            <h3 class="text-magenta"><?php echo $totalOrders; ?></h3>
                            <p class="text-off-white mb-0">Total Orders</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-yellow mb-3"></i>
                            <h3 class="text-yellow"><?php echo $totalCustomers; ?></h3>
                            <p class="text-off-white mb-0">Total Customers</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-box fa-3x text-cyan mb-3"></i>
                            <h3 class="text-cyan"><?php echo $totalProducts; ?></h3>
                            <p class="text-off-white mb-0">Total Products</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row g-4 mb-5">
                <!-- Monthly Revenue Chart -->
                <div class="col-lg-8">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-chart-line me-2"></i>
                                Monthly Revenue (<?php echo $currentYear; ?>)
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="revenueChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Order Status Distribution -->
                <div class="col-lg-4">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-chart-pie me-2"></i>
                                Order Status
                            </h5>
                        </div>
                        <div class="card-body">
                            <canvas id="statusChart" width="300" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Products & Recent Activity -->
            <div class="row g-4">
                <!-- Top Selling Products -->
                <div class="col-lg-6">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-header bg-dark-grey-2 border-yellow">
                            <h5 class="mb-0 text-yellow">
                                <i class="fas fa-trophy me-2"></i>
                                Top Selling Products
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($topProducts)): ?>
                                <div class="table-responsive">
                                    <table class="table table-dark table-sm">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Sold</th>
                                                <th>Revenue</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($topProducts as $productData): ?>
                                                <tr>
                                                    <td>
                                                        <strong class="text-white"><?php echo htmlspecialchars($productData['product']['name']); ?></strong>
                                                    </td>
                                                    <td class="text-cyan"><?php echo $productData['quantity']; ?></td>
                                                    <td class="text-yellow">$<?php echo number_format($productData['revenue'], 2); ?></td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <p class="text-off-white text-center">No sales data available</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="col-lg-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-header bg-dark-grey-2 border-cyan">
                            <h5 class="mb-0 text-cyan">
                                <i class="fas fa-clock me-2"></i>
                                Recent Activity
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($recentOrders)): ?>
                                <div class="activity-list">
                                    <?php foreach (array_slice($recentOrders, 0, 5) as $order): ?>
                                        <div class="activity-item d-flex align-items-center mb-3">
                                            <div class="activity-icon me-3">
                                                <i class="fas fa-shopping-cart text-magenta"></i>
                                            </div>
                                            <div class="activity-content flex-grow-1">
                                                <div class="text-white">
                                                    Order #<?php echo htmlspecialchars($order['id']); ?>
                                                </div>
                                                <div class="text-off-white small">
                                                    <?php echo htmlspecialchars($order['customer_name']); ?> - 
                                                    $<?php echo number_format($order['total'], 2); ?>
                                                </div>
                                            </div>
                                            <div class="activity-time text-muted small">
                                                <?php echo date('M j', strtotime($order['created_at'])); ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php else: ?>
                                <p class="text-off-white text-center">No recent activity</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Monthly Revenue Chart
const revenueCtx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(revenueCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [{
            label: 'Revenue',
            data: [
                <?php echo implode(',', array_values($monthlyRevenue)); ?>
            ],
            borderColor: '#00FFFF',
            backgroundColor: 'rgba(0, 255, 255, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                labels: {
                    color: '#ffffff'
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    color: '#ffffff',
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            x: {
                ticks: {
                    color: '#ffffff'
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        }
    }
});

// Order Status Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: [
            <?php echo implode(',', array_map(function($status) { return '"' . ucfirst($status) . '"'; }, array_keys($statusCounts))); ?>
        ],
        datasets: [{
            data: [
                <?php echo implode(',', array_values($statusCounts)); ?>
            ],
            backgroundColor: [
                '#FFA500', // pending
                '#17A2B8', // processing
                '#007BFF', // shipped
                '#28A745', // delivered
                '#DC3545'  // cancelled
            ],
            borderWidth: 2,
            borderColor: '#333'
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    color: '#ffffff',
                    padding: 20
                }
            }
        }
    }
});
</script>


