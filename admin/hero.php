<?php
/**
 * CYPTSHOP Hero Management System
 * Manage hero banners and sub-hero content
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
session_start();
requireAdmin();

// Get hero data from database
try {
    $heroData = getHeroData();
} catch (Exception $e) {
    error_log('Hero data error: ' . $e->getMessage());
    $heroData = [
        'main_hero' => [
            'id' => 'hero_main',
            'title' => 'Custom T-Shirt Printing',
            'subtitle' => 'Detroit Style, Premium Quality',
            'description' => 'Transform your ideas into wearable art with our professional custom t-shirt printing services.',
            'background_type' => 'image',
            'background_image' => 'hero-bg.jpg',
            'background_video' => '',
            'overlay_opacity' => 0.6,
            'text_position' => 'center',
            'text_color' => '#ffffff',
            'cta_text' => 'Start Your Design',
            'cta_link' => '/products.php',
            'cta_style' => 'btn-cyan',
            'active' => true,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ],
        'sub_heroes' => [
            [
                'id' => 'sub_hero_1',
                'page' => 'products',
                'title' => 'Browse Our Collection',
                'subtitle' => 'Premium Quality T-Shirts',
                'background_image' => 'products-hero.jpg',
                'active' => true,
                'created_at' => date('Y-m-d H:i:s')
            ],
            [
                'id' => 'sub_hero_2',
                'page' => 'portfolio',
                'title' => 'Our Work',
                'subtitle' => 'See What We\'ve Created',
                'background_image' => 'portfolio-hero.jpg',
                'active' => true,
                'created_at' => date('Y-m-d H:i:s')
            ]
        ]
    ];
}

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'update_main_hero':
                // Handle file upload for background image
                $uploadedImage = '';
                if (isset($_FILES['background_image_file']) && $_FILES['background_image_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroImageUpload($_FILES['background_image_file'], 'main_hero');
                    if ($uploadResult['success']) {
                        $uploadedImage = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                $heroData['main_hero']['title'] = trim($_POST['title'] ?? '');
                $heroData['main_hero']['subtitle'] = trim($_POST['subtitle'] ?? '');
                $heroData['main_hero']['description'] = trim($_POST['description'] ?? '');
                $heroData['main_hero']['background_type'] = $_POST['background_type'] ?? 'image';

                // Use uploaded image if available, otherwise use manual input
                if ($uploadedImage) {
                    $heroData['main_hero']['background_image'] = $uploadedImage;
                } else {
                    $heroData['main_hero']['background_image'] = trim($_POST['background_image'] ?? '');
                }

                $heroData['main_hero']['background_video'] = trim($_POST['background_video'] ?? '');
                $heroData['main_hero']['overlay_opacity'] = floatval($_POST['overlay_opacity'] ?? 0.6);
                $heroData['main_hero']['text_position'] = $_POST['text_position'] ?? 'center';
                $heroData['main_hero']['text_color'] = $_POST['text_color'] ?? '#ffffff';
                $heroData['main_hero']['cta_text'] = trim($_POST['cta_text'] ?? '');
                $heroData['main_hero']['cta_link'] = trim($_POST['cta_link'] ?? '');
                $heroData['main_hero']['cta_style'] = $_POST['cta_style'] ?? 'btn-cyan';
                $heroData['main_hero']['active'] = isset($_POST['active']);
                $heroData['main_hero']['updated_at'] = date('Y-m-d H:i:s');

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Main hero updated successfully!';
                    } else {
                        $error = 'Failed to update main hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'add_sub_hero':
                // Handle file upload for sub-hero background image
                $uploadedSubImage = '';
                if (isset($_FILES['sub_background_image_file']) && $_FILES['sub_background_image_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroImageUpload($_FILES['sub_background_image_file'], 'sub_hero');
                    if ($uploadResult['success']) {
                        $uploadedSubImage = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                $newSubHero = [
                    'id' => 'sub_hero_' . uniqid(),
                    'page' => trim($_POST['page'] ?? ''),
                    'title' => trim($_POST['title'] ?? ''),
                    'subtitle' => trim($_POST['subtitle'] ?? ''),
                    'background_image' => $uploadedSubImage ?: trim($_POST['background_image'] ?? ''),
                    'active' => isset($_POST['active']),
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if (empty($newSubHero['page']) || empty($newSubHero['title'])) {
                    $error = 'Page and title are required for sub-heroes.';
                } else {
                    $heroData['sub_heroes'][] = $newSubHero;

                    try {
                        if (updateHeroData($heroData)) {
                            $success = 'Sub-hero added successfully!';
                        } else {
                            $error = 'Failed to add sub-hero.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'update_sub_hero':
                $subHeroId = $_POST['sub_hero_id'] ?? '';

                // Handle file upload for sub-hero background image
                $uploadedSubImage = '';
                if (isset($_FILES['sub_background_image_file']) && $_FILES['sub_background_image_file']['error'] === UPLOAD_ERR_OK) {
                    $uploadResult = handleHeroImageUpload($_FILES['sub_background_image_file'], 'sub_hero');
                    if ($uploadResult['success']) {
                        $uploadedSubImage = $uploadResult['filename'];
                    } else {
                        $error = $uploadResult['error'];
                        break;
                    }
                }

                foreach ($heroData['sub_heroes'] as &$subHero) {
                    if ($subHero['id'] === $subHeroId) {
                        $subHero['page'] = trim($_POST['page'] ?? '');
                        $subHero['title'] = trim($_POST['title'] ?? '');
                        $subHero['subtitle'] = trim($_POST['subtitle'] ?? '');

                        // Use uploaded image if available, otherwise use manual input
                        if ($uploadedSubImage) {
                            $subHero['background_image'] = $uploadedSubImage;
                        } else {
                            $subHero['background_image'] = trim($_POST['background_image'] ?? '');
                        }

                        $subHero['active'] = isset($_POST['active']);
                        $subHero['updated_at'] = date('Y-m-d H:i:s');
                        break;
                    }
                }

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Sub-hero updated successfully!';
                    } else {
                        $error = 'Failed to update sub-hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'delete_sub_hero':
                $subHeroId = $_POST['sub_hero_id'] ?? '';
                $heroData['sub_heroes'] = array_filter($heroData['sub_heroes'], function($sh) use ($subHeroId) {
                    return $sh['id'] !== $subHeroId;
                });

                try {
                    if (updateHeroData($heroData)) {
                        $success = 'Sub-hero deleted successfully!';
                    } else {
                        $error = 'Failed to delete sub-hero.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;
        }
    }
}

$pageTitle = 'Hero Management - Admin';
$bodyClass = 'admin-hero';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Enhanced text contrast for dark mode */
.text-white-50 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.card-body .text-white-50 {
    color: rgba(255, 255, 255, 0.85) !important;
}

.small.text-white-50 {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Better contrast for sub-hero cards */
.card.bg-dark-grey-2 .card-body {
    background: rgba(45, 45, 45, 0.95);
}

.card.bg-dark-grey-2 .card-body p {
    line-height: 1.5;
}

/* Form elements styling */
.form-control, .form-select {
    background-color: #2d2d2d;
    border-color: #404040;
    color: #ffffff;
}

.form-control:focus, .form-select:focus {
    background-color: #404040;
    border-color: #00FFFF;
    color: #ffffff;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25);
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Hero Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-cyan" onclick="previewHero()">
                        <i class="fas fa-eye me-2"></i>Preview
                    </button>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Main Hero Section -->
            <div class="card bg-dark-grey-1 border-magenta mb-4">
                <div class="card-header bg-dark-grey-2 border-magenta">
                    <h5 class="mb-0 text-magenta">
                        <i class="fas fa-star me-2"></i>Main Hero Banner
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <input type="hidden" name="action" value="update_main_hero">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="row g-3">
                            <!-- Title -->
                            <div class="col-md-6">
                                <label for="heroTitle" class="form-label text-white">Title *</label>
                                <input type="text" class="form-control" id="heroTitle" name="title"
                                       value="<?php echo htmlspecialchars($heroData['main_hero']['title']); ?>" required>
                            </div>

                            <!-- Subtitle -->
                            <div class="col-md-6">
                                <label for="heroSubtitle" class="form-label text-white">Subtitle</label>
                                <input type="text" class="form-control" id="heroSubtitle" name="subtitle"
                                       value="<?php echo htmlspecialchars($heroData['main_hero']['subtitle']); ?>">
                            </div>

                            <!-- Description -->
                            <div class="col-12">
                                <label for="heroDescription" class="form-label text-white">Description</label>
                                <textarea class="form-control" id="heroDescription" name="description" rows="3"><?php echo htmlspecialchars($heroData['main_hero']['description']); ?></textarea>
                            </div>

                            <!-- Background Type -->
                            <div class="col-md-4">
                                <label for="backgroundType" class="form-label text-white">Background Type</label>
                                <select class="form-select" id="backgroundType" name="background_type" onchange="toggleBackgroundFields()">
                                    <option value="image" <?php echo $heroData['main_hero']['background_type'] === 'image' ? 'selected' : ''; ?>>Image</option>
                                    <option value="video" <?php echo $heroData['main_hero']['background_type'] === 'video' ? 'selected' : ''; ?>>Video</option>
                                </select>
                            </div>

                            <!-- Background Image -->
                            <div class="col-md-4" id="imageField">
                                <label for="backgroundImage" class="form-label text-white">Background Image</label>
                                <div class="mb-2">
                                    <input type="file" class="form-control" id="backgroundImageFile" name="background_image_file"
                                           accept="image/*" onchange="previewImage(this, 'mainHeroPreview')">
                                    <small class="text-white-50">Upload new image (JPG, PNG, WebP - Max 5MB)</small>
                                </div>
                                <div class="mb-2">
                                    <input type="text" class="form-control" id="backgroundImage" name="background_image"
                                           value="<?php echo htmlspecialchars($heroData['main_hero']['background_image']); ?>"
                                           placeholder="Or enter image filename manually">
                                </div>
                                <?php if (!empty($heroData['main_hero']['background_image'])): ?>
                                    <div class="current-image">
                                        <small class="text-cyan">Current: <?php echo htmlspecialchars($heroData['main_hero']['background_image']); ?></small>
                                        <div id="mainHeroPreview" class="mt-2">
                                            <img src="<?php echo SITE_URL; ?>/assets/images/hero/<?php echo htmlspecialchars($heroData['main_hero']['background_image']); ?>"
                                                 alt="Current hero image" class="img-thumbnail" style="max-width: 200px; max-height: 100px;">
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div id="mainHeroPreview" class="mt-2"></div>
                                <?php endif; ?>
                            </div>

                            <!-- Background Video -->
                            <div class="col-md-4" id="videoField" style="display: none;">
                                <label for="backgroundVideo" class="form-label text-white">Background Video</label>
                                <input type="text" class="form-control" id="backgroundVideo" name="background_video"
                                       value="<?php echo htmlspecialchars($heroData['main_hero']['background_video'] ?? ''); ?>"
                                       placeholder="hero-video.mp4">
                            </div>

                            <!-- Overlay Opacity -->
                            <div class="col-md-4">
                                <label for="overlayOpacity" class="form-label text-white">Overlay Opacity</label>
                                <input type="range" class="form-range" id="overlayOpacity" name="overlay_opacity"
                                       min="0" max="1" step="0.1" value="<?php echo $heroData['main_hero']['overlay_opacity']; ?>"
                                       oninput="updateOpacityValue(this.value)">
                                <small class="text-white-50">Current: <span id="opacityValue"><?php echo $heroData['main_hero']['overlay_opacity']; ?></span></small>
                            </div>

                            <!-- Text Position -->
                            <div class="col-md-4">
                                <label for="textPosition" class="form-label text-white">Text Position</label>
                                <select class="form-select" id="textPosition" name="text_position">
                                    <option value="left" <?php echo $heroData['main_hero']['text_position'] === 'left' ? 'selected' : ''; ?>>Left</option>
                                    <option value="center" <?php echo $heroData['main_hero']['text_position'] === 'center' ? 'selected' : ''; ?>>Center</option>
                                    <option value="right" <?php echo $heroData['main_hero']['text_position'] === 'right' ? 'selected' : ''; ?>>Right</option>
                                </select>
                            </div>

                            <!-- Text Color -->
                            <div class="col-md-4">
                                <label for="textColor" class="form-label text-white">Text Color</label>
                                <input type="color" class="form-control form-control-color" id="textColor" name="text_color"
                                       value="<?php echo $heroData['main_hero']['text_color']; ?>">
                            </div>

                            <!-- CTA Button -->
                            <div class="col-md-4">
                                <label for="ctaText" class="form-label text-white">CTA Button Text</label>
                                <input type="text" class="form-control" id="ctaText" name="cta_text"
                                       value="<?php echo htmlspecialchars($heroData['main_hero']['cta_text']); ?>">
                            </div>

                            <!-- CTA Link -->
                            <div class="col-md-4">
                                <label for="ctaLink" class="form-label text-white">CTA Link</label>
                                <input type="text" class="form-control" id="ctaLink" name="cta_link"
                                       value="<?php echo htmlspecialchars($heroData['main_hero']['cta_link']); ?>">
                            </div>

                            <!-- CTA Style -->
                            <div class="col-md-4">
                                <label for="ctaStyle" class="form-label text-white">CTA Style</label>
                                <select class="form-select" id="ctaStyle" name="cta_style">
                                    <option value="btn-cyan" <?php echo $heroData['main_hero']['cta_style'] === 'btn-cyan' ? 'selected' : ''; ?>>Cyan</option>
                                    <option value="btn-magenta" <?php echo $heroData['main_hero']['cta_style'] === 'btn-magenta' ? 'selected' : ''; ?>>Magenta</option>
                                    <option value="btn-yellow" <?php echo $heroData['main_hero']['cta_style'] === 'btn-yellow' ? 'selected' : ''; ?>>Yellow</option>
                                </select>
                            </div>

                            <!-- Active Status -->
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="heroActive" name="active"
                                           <?php echo $heroData['main_hero']['active'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label text-white" for="heroActive">
                                        Active Hero Banner
                                    </label>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12">
                                <button type="submit" class="btn btn-magenta">
                                    <i class="fas fa-save me-2"></i>Update Main Hero
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sub-Heroes Section -->
            <div class="card bg-dark-grey-1 border-cyan">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-images me-2"></i>Sub-Hero Banners
                        </h5>
                        <button type="button" class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#subHeroModal">
                            <i class="fas fa-plus me-2"></i>Add Sub-Hero
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <?php foreach ($heroData['sub_heroes'] as $subHero): ?>
                            <div class="col-lg-6 mb-3">
                                <div class="card bg-dark-grey-2 border-yellow">
                                    <div class="card-header bg-dark-grey-3 border-yellow">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0 text-yellow"><?php echo htmlspecialchars($subHero['title']); ?></h6>
                                            <span class="badge bg-<?php echo $subHero['active'] ? 'success' : 'secondary'; ?>">
                                                <?php echo $subHero['active'] ? 'Active' : 'Inactive'; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <p class="text-white mb-1">
                                            <strong>Page:</strong> <?php echo htmlspecialchars($subHero['page']); ?>
                                        </p>
                                        <p class="text-white-50 mb-2"><?php echo htmlspecialchars($subHero['subtitle']); ?></p>
                                        <p class="text-white-50 small mb-3">
                                            <strong>Image:</strong> <?php echo htmlspecialchars($subHero['background_image']); ?>
                                        </p>

                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-cyan" onclick="editSubHero('<?php echo $subHero['id']; ?>')">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteSubHero('<?php echo $subHero['id']; ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
    </div>
</div>

<script>
function toggleBackgroundFields() {
    const type = document.getElementById('backgroundType').value;
    const imageField = document.getElementById('imageField');
    const videoField = document.getElementById('videoField');

    if (type === 'video') {
        imageField.style.display = 'none';
        videoField.style.display = 'block';
    } else {
        imageField.style.display = 'block';
        videoField.style.display = 'none';
    }
}

function updateOpacityValue(value) {
    document.getElementById('opacityValue').textContent = value;
}

function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);

    if (input.files && input.files[0]) {
        const reader = new FileReader();

        reader.onload = function(e) {
            preview.innerHTML = `
                <img src="${e.target.result}" alt="Image preview"
                     class="img-thumbnail" style="max-width: 200px; max-height: 100px;">
                <small class="text-success d-block mt-1">
                    <i class="fas fa-check me-1"></i>New image selected: ${input.files[0].name}
                </small>
            `;
        };

        reader.readAsDataURL(input.files[0]);
    } else {
        preview.innerHTML = '';
    }
}

function previewHero() {
    window.open('/', '_blank');
}

function editSubHero(subHeroId) {
    // Find sub-hero data
    const subHeroes = <?php echo json_encode($heroData['sub_heroes']); ?>;
    const subHero = subHeroes.find(sh => sh.id === subHeroId);

    if (subHero) {
        document.getElementById('modalTitle').textContent = 'Edit Sub-Hero';
        document.getElementById('formAction').value = 'update_sub_hero';
        document.getElementById('subHeroId').value = subHero.id;
        document.getElementById('subHeroPage').value = subHero.page;
        document.getElementById('subHeroTitle').value = subHero.title;
        document.getElementById('subHeroSubtitle').value = subHero.subtitle;
        document.getElementById('subHeroImage').value = subHero.background_image;
        document.getElementById('subHeroActive').checked = subHero.active;

        new bootstrap.Modal(document.getElementById('subHeroModal')).show();
    }
}

function deleteSubHero(subHeroId) {
    if (confirm('Are you sure you want to delete this sub-hero? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_sub_hero">
            <input type="hidden" name="sub_hero_id" value="${subHeroId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Reset modal when hidden
document.getElementById('subHeroModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('subHeroForm').reset();
    document.getElementById('modalTitle').textContent = 'Add Sub-Hero';
    document.getElementById('formAction').value = 'add_sub_hero';
    document.getElementById('subHeroId').value = '';
});

// Initialize background fields on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleBackgroundFields();
});
</script>

<!-- Sub-Hero Modal -->
<div class="modal fade" id="subHeroModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header bg-dark-grey-2 border-cyan">
                <h5 class="modal-title text-cyan" id="modalTitle">
                    <i class="fas fa-image me-2"></i>Add Sub-Hero
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="subHeroForm" method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add_sub_hero">
                    <input type="hidden" name="sub_hero_id" id="subHeroId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="subHeroPage" class="form-label text-white">Page *</label>
                            <select class="form-select" id="subHeroPage" name="page" required>
                                <option value="">Select Page</option>
                                <option value="products">Products</option>
                                <option value="portfolio">Portfolio</option>
                                <option value="contact">Contact</option>
                                <option value="about">About</option>
                                <option value="services">Services</option>
                            </select>
                        </div>

                        <div class="col-md-6">
                            <label for="subHeroTitle" class="form-label text-white">Title *</label>
                            <input type="text" class="form-control" id="subHeroTitle" name="title" required>
                        </div>

                        <div class="col-12">
                            <label for="subHeroSubtitle" class="form-label text-white">Subtitle</label>
                            <input type="text" class="form-control" id="subHeroSubtitle" name="subtitle">
                        </div>

                        <div class="col-12">
                            <label for="subHeroImage" class="form-label text-white">Background Image</label>
                            <div class="mb-2">
                                <input type="file" class="form-control" id="subHeroImageFile" name="sub_background_image_file"
                                       accept="image/*" onchange="previewImage(this, 'subHeroPreview')">
                                <small class="text-white-50">Upload new image (JPG, PNG, WebP - Max 5MB)</small>
                            </div>
                            <div class="mb-2">
                                <input type="text" class="form-control" id="subHeroImage" name="background_image"
                                       placeholder="Or enter image filename manually">
                            </div>
                            <div id="subHeroPreview" class="mt-2"></div>
                        </div>

                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="subHeroActive" name="active" checked>
                                <label class="form-check-label text-white" for="subHeroActive">
                                    Active Sub-Hero
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-cyan">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Sub-Hero
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>


