<?php
/**
 * CYPTSHOP File Version Control System
 * Track and manage file versions for customer uploads
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/db.php';

// Start session and require admin access
session_start();
requireAdmin();

// Initialize file versions
$fileVersionsFile = BASE_PATH . 'assets/data/file_versions.json';
if (!file_exists($fileVersionsFile)) {
    saveJsonData($fileVersionsFile, []);
}

$fileVersions = getJsonData($fileVersionsFile);
$customerUploads = getJsonData(CUSTOMER_UPLOADS_JSON);

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create_version':
                $uploadId = $_POST['upload_id'] ?? '';
                $versionNotes = trim($_POST['version_notes'] ?? '');
                
                // Find the upload
                $upload = null;
                foreach ($customerUploads as $uploadItem) {
                    if ($uploadItem['id'] === $uploadId) {
                        $upload = $uploadItem;
                        break;
                    }
                }
                
                if ($upload) {
                    // Get current version number
                    $currentVersions = array_filter($fileVersions, function($version) use ($uploadId) {
                        return $version['upload_id'] === $uploadId;
                    });
                    
                    $versionNumber = count($currentVersions) + 1;
                    
                    // Create version record
                    $version = [
                        'id' => 'version_' . uniqid(),
                        'upload_id' => $uploadId,
                        'version_number' => $versionNumber,
                        'filename' => $upload['filename'],
                        'original_name' => $upload['original_name'],
                        'file_size' => $upload['file_size'],
                        'created_by' => getCurrentUser()['email'],
                        'version_notes' => $versionNotes,
                        'created_at' => date('Y-m-d H:i:s'),
                        'is_current' => true
                    ];
                    
                    // Mark previous versions as not current
                    foreach ($fileVersions as &$existingVersion) {
                        if ($existingVersion['upload_id'] === $uploadId) {
                            $existingVersion['is_current'] = false;
                        }
                    }
                    
                    $fileVersions[] = $version;
                    
                    if (saveJsonData($fileVersionsFile, $fileVersions)) {
                        $success = 'File version created successfully!';
                    } else {
                        $error = 'Failed to create file version.';
                    }
                } else {
                    $error = 'File not found.';
                }
                break;
                
            case 'restore_version':
                $versionId = $_POST['version_id'] ?? '';
                
                // Find the version
                $targetVersion = null;
                foreach ($fileVersions as $version) {
                    if ($version['id'] === $versionId) {
                        $targetVersion = $version;
                        break;
                    }
                }
                
                if ($targetVersion) {
                    // Mark all versions of this upload as not current
                    foreach ($fileVersions as &$version) {
                        if ($version['upload_id'] === $targetVersion['upload_id']) {
                            $version['is_current'] = false;
                        }
                    }
                    
                    // Mark target version as current
                    foreach ($fileVersions as &$version) {
                        if ($version['id'] === $versionId) {
                            $version['is_current'] = true;
                            $version['restored_at'] = date('Y-m-d H:i:s');
                            $version['restored_by'] = getCurrentUser()['email'];
                            break;
                        }
                    }
                    
                    if (saveJsonData($fileVersionsFile, $fileVersions)) {
                        $success = 'File version restored successfully!';
                    } else {
                        $error = 'Failed to restore file version.';
                    }
                } else {
                    $error = 'Version not found.';
                }
                break;
        }
    }
}

// Group versions by upload ID
$versionsByUpload = [];
foreach ($fileVersions as $version) {
    $versionsByUpload[$version['upload_id']][] = $version;
}

// Sort versions by version number (descending)
foreach ($versionsByUpload as &$versions) {
    usort($versions, function($a, $b) {
        return $b['version_number'] - $a['version_number'];
    });
}

$pageTitle = 'File Version Control - Admin';
$bodyClass = 'admin-file-versions';

include BASE_PATH . 'includes/header.php';
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">File Version Control</h1>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="row g-4 mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-file fa-2x text-cyan mb-2"></i>
                            <h4 class="text-cyan"><?php echo count($customerUploads); ?></h4>
                            <p class="text-off-white mb-0">Total Files</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-magenta">
                        <div class="card-body text-center">
                            <i class="fas fa-code-branch fa-2x text-magenta mb-2"></i>
                            <h4 class="text-magenta"><?php echo count($fileVersions); ?></h4>
                            <p class="text-off-white mb-0">Total Versions</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-yellow">
                        <div class="card-body text-center">
                            <i class="fas fa-files fa-2x text-yellow mb-2"></i>
                            <h4 class="text-yellow"><?php echo count($versionsByUpload); ?></h4>
                            <p class="text-off-white mb-0">Files with Versions</p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="card bg-dark-grey-1 border-cyan">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x text-cyan mb-2"></i>
                            <h4 class="text-cyan">
                                <?php 
                                if (!empty($fileVersions)) {
                                    $latest = max(array_column($fileVersions, 'created_at'));
                                    echo date('H:i', strtotime($latest));
                                } else {
                                    echo '--:--';
                                }
                                ?>
                            </h4>
                            <p class="text-off-white mb-0">Last Version</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- File Versions -->
            <?php if (!empty($versionsByUpload)): ?>
                <?php foreach ($versionsByUpload as $uploadId => $versions): ?>
                    <?php 
                    $upload = null;
                    foreach ($customerUploads as $u) {
                        if ($u['id'] === $uploadId) {
                            $upload = $u;
                            break;
                        }
                    }
                    ?>
                    
                    <div class="card bg-dark-grey-1 border-magenta mb-4">
                        <div class="card-header bg-dark-grey-2 border-magenta">
                            <h5 class="mb-0 text-magenta">
                                <i class="fas fa-file me-2"></i>
                                <?php echo htmlspecialchars($upload['original_name'] ?? 'Unknown File'); ?>
                                <span class="badge bg-cyan text-black float-end"><?php echo count($versions); ?> versions</span>
                            </h5>
                            <div class="text-off-white small">
                                Order: <?php echo htmlspecialchars($upload['order_id'] ?? 'Unknown'); ?> | 
                                Customer: <?php echo htmlspecialchars($upload['customer_email'] ?? 'Unknown'); ?>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="version-timeline">
                                <?php foreach ($versions as $version): ?>
                                    <div class="version-item d-flex align-items-center mb-3 p-3 bg-dark-grey-2 rounded <?php echo $version['is_current'] ? 'border border-success' : ''; ?>">
                                        <div class="version-icon me-3">
                                            <div class="version-number bg-magenta text-black rounded-circle d-flex align-items-center justify-content-center" 
                                                 style="width: 40px; height: 40px; font-weight: bold;">
                                                v<?php echo $version['version_number']; ?>
                                            </div>
                                        </div>
                                        
                                        <div class="version-details flex-grow-1">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="text-white mb-1">
                                                        Version <?php echo $version['version_number']; ?>
                                                        <?php if ($version['is_current']): ?>
                                                            <span class="badge bg-success ms-2">Current</span>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="text-off-white small">
                                                        <strong>Created:</strong> <?php echo date('M j, Y g:i A', strtotime($version['created_at'])); ?><br>
                                                        <strong>By:</strong> <?php echo htmlspecialchars($version['created_by']); ?><br>
                                                        <strong>Size:</strong> <?php echo round($version['file_size'] / 1024, 1); ?> KB
                                                        <?php if (!empty($version['version_notes'])): ?>
                                                            <br><strong>Notes:</strong> <?php echo htmlspecialchars($version['version_notes']); ?>
                                                        <?php endif; ?>
                                                        <?php if (isset($version['restored_at'])): ?>
                                                            <br><strong>Restored:</strong> <?php echo date('M j, Y g:i A', strtotime($version['restored_at'])); ?>
                                                            by <?php echo htmlspecialchars($version['restored_by']); ?>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                
                                                <div class="version-actions">
                                                    <?php if (!$version['is_current']): ?>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="restore_version">
                                                            <input type="hidden" name="version_id" value="<?php echo $version['id']; ?>">
                                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-success" 
                                                                    onclick="return confirm('Restore this version as current?')">
                                                                <i class="fas fa-undo me-1"></i>Restore
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>
                                                    
                                                    <a href="<?php echo SITE_URL; ?>/uploads/orders/<?php echo $version['filename']; ?>" 
                                                       class="btn btn-sm btn-outline-cyan ms-2" target="_blank">
                                                        <i class="fas fa-download me-1"></i>Download
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <!-- Create New Version -->
                            <div class="mt-3">
                                <button class="btn btn-magenta" onclick="createVersion('<?php echo $uploadId; ?>')">
                                    <i class="fas fa-plus me-2"></i>Create New Version
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-code-branch fa-4x text-off-white mb-4"></i>
                        <h4 class="text-white">No File Versions</h4>
                        <p class="text-off-white">File versions will appear here when created.</p>
                    </div>
                </div>
            <?php endif; ?>
    </div>
</div>

<!-- Create Version Modal -->
<div class="modal fade" id="versionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-magenta">
            <div class="modal-header bg-dark-grey-2 border-magenta">
                <h5 class="modal-title text-magenta">
                    <i class="fas fa-plus me-2"></i>
                    Create New Version
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="create_version">
                    <input type="hidden" name="upload_id" id="versionUploadId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="versionNotes" class="form-label text-white">Version Notes</label>
                        <textarea class="form-control" id="versionNotes" name="version_notes" rows="4" 
                                  placeholder="Describe what changed in this version..."></textarea>
                    </div>
                    
                    <div class="alert alert-info bg-dark-grey-2 border-cyan text-cyan">
                        <i class="fas fa-info-circle me-2"></i>
                        This will create a snapshot of the current file state as a new version.
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-magenta">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-magenta">
                        <i class="fas fa-plus me-2"></i>Create Version
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function createVersion(uploadId) {
    document.getElementById('versionUploadId').value = uploadId;
    new bootstrap.Modal(document.getElementById('versionModal')).show();
}
</script>


