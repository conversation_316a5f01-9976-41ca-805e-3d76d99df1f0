<?php
/**
 * CYPTSHOP Database Backup & Restore System
 * Real MySQL backup functionality with compression and scheduling
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

$success = '';
$error = '';

// Backup directory
$backupDir = BASE_PATH . 'backups/';
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        switch ($action) {
            case 'create_backup':
                $backupName = trim($_POST['backup_name'] ?? '');
                $includeData = isset($_POST['include_data']);
                $compress = isset($_POST['compress']);
                
                if (empty($backupName)) {
                    $backupName = 'backup_' . date('Y-m-d_H-i-s');
                }
                
                $result = createDatabaseBackup($backupName, $includeData, $compress);
                if ($result['success']) {
                    $success = $result['message'];
                } else {
                    $error = $result['message'];
                }
                break;
                
            case 'restore_backup':
                $backupFile = $_POST['backup_file'] ?? '';
                if (empty($backupFile)) {
                    $error = 'Please select a backup file to restore.';
                } else {
                    $result = restoreDatabaseBackup($backupFile);
                    if ($result['success']) {
                        $success = $result['message'];
                    } else {
                        $error = $result['message'];
                    }
                }
                break;
                
            case 'delete_backup':
                $backupFile = $_POST['backup_file'] ?? '';
                if (empty($backupFile)) {
                    $error = 'Please select a backup file to delete.';
                } else {
                    $filePath = $backupDir . $backupFile;
                    if (file_exists($filePath) && unlink($filePath)) {
                        $success = 'Backup file deleted successfully.';
                    } else {
                        $error = 'Failed to delete backup file.';
                    }
                }
                break;
                
            case 'download_backup':
                $backupFile = $_POST['backup_file'] ?? '';
                if (!empty($backupFile)) {
                    $filePath = $backupDir . $backupFile;
                    if (file_exists($filePath)) {
                        header('Content-Type: application/octet-stream');
                        header('Content-Disposition: attachment; filename="' . $backupFile . '"');
                        header('Content-Length: ' . filesize($filePath));
                        readfile($filePath);
                        exit;
                    } else {
                        $error = 'Backup file not found.';
                    }
                }
                break;
        }
    }
}

// Get existing backup files
$backupFiles = getBackupFiles();

// Database info
$dbInfo = getDatabaseInfo();

$pageTitle = 'Database Backup & Restore - Admin';
$bodyClass = 'admin-backup';

include BASE_PATH . 'includes/header.php';

// Add debugging information
if (isset($_GET['debug'])) {
    echo "<pre>";
    echo "Database Available: " . (isDatabaseAvailable() ? 'YES' : 'NO') . "\n";
    echo "Backup Directory: " . $backupDir . "\n";
    echo "Directory Writable: " . (is_writable($backupDir) ? 'YES' : 'NO') . "\n";
    echo "DB_NAME: " . (defined('DB_NAME') ? DB_NAME : 'NOT DEFINED') . "\n";
    echo "</pre>";
}

/**
 * Create database backup
 */
function createDatabaseBackup($name, $includeData = true, $compress = false) {
    global $backupDir;
    
    if (!isDatabaseAvailable()) {
        return ['success' => false, 'message' => 'Database connection not available.'];
    }
    
    try {
        $pdo = getDatabaseConnection();
        $filename = $name . '.sql';
        if ($compress) {
            $filename .= '.gz';
        }
        
        $filePath = $backupDir . $filename;
        
        // Get all tables
        $tables = [];
        $stmt = $pdo->query("SHOW TABLES");
        while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
            $tables[] = $row[0];
        }
        
        $output = "-- CYPTSHOP Database Backup\n";
        $output .= "-- Generated on: " . date('Y-m-d H:i:s') . "\n";
        $output .= "-- Database: " . DB_NAME . "\n\n";
        $output .= "SET FOREIGN_KEY_CHECKS=0;\n\n";
        
        foreach ($tables as $table) {
            // Get table structure
            $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
            $row = $stmt->fetch(PDO::FETCH_NUM);
            $output .= "-- Table structure for `$table`\n";
            $output .= "DROP TABLE IF EXISTS `$table`;\n";
            $output .= $row[1] . ";\n\n";
            
            if ($includeData) {
                // Get table data
                $stmt = $pdo->query("SELECT * FROM `$table`");
                $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($rows)) {
                    $output .= "-- Data for table `$table`\n";
                    
                    foreach ($rows as $row) {
                        $values = array_map(function($value) use ($pdo) {
                            return $value === null ? 'NULL' : $pdo->quote($value);
                        }, array_values($row));
                        
                        $output .= "INSERT INTO `$table` VALUES (" . implode(', ', $values) . ");\n";
                    }
                    $output .= "\n";
                }
            }
        }
        
        $output .= "SET FOREIGN_KEY_CHECKS=1;\n";
        
        // Write to file
        if ($compress) {
            $gz = gzopen($filePath, 'w9');
            gzwrite($gz, $output);
            gzclose($gz);
        } else {
            file_put_contents($filePath, $output);
        }
        
        return [
            'success' => true, 
            'message' => "Backup created successfully: $filename (" . formatBytes(filesize($filePath)) . ")"
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Backup failed: ' . $e->getMessage()];
    }
}

/**
 * Restore database backup
 */
function restoreDatabaseBackup($filename) {
    global $backupDir;
    
    if (!isDatabaseAvailable()) {
        return ['success' => false, 'message' => 'Database connection not available.'];
    }
    
    $filePath = $backupDir . $filename;
    if (!file_exists($filePath)) {
        return ['success' => false, 'message' => 'Backup file not found.'];
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Read SQL file
        if (pathinfo($filename, PATHINFO_EXTENSION) === 'gz') {
            $sql = gzfile($filePath);
            $sql = implode('', $sql);
        } else {
            $sql = file_get_contents($filePath);
        }
        
        // Execute SQL statements
        $statements = explode(';', $sql);
        $executed = 0;
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                $pdo->exec($statement);
                $executed++;
            }
        }
        
        return [
            'success' => true, 
            'message' => "Database restored successfully from $filename ($executed statements executed)"
        ];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Restore failed: ' . $e->getMessage()];
    }
}

/**
 * Get backup files
 */
function getBackupFiles() {
    global $backupDir;
    
    $files = [];
    if (is_dir($backupDir)) {
        $items = scandir($backupDir);
        foreach ($items as $item) {
            if ($item !== '.' && $item !== '..' && is_file($backupDir . $item)) {
                $files[] = [
                    'name' => $item,
                    'size' => filesize($backupDir . $item),
                    'date' => filemtime($backupDir . $item)
                ];
            }
        }
        
        // Sort by date (newest first)
        usort($files, function($a, $b) {
            return $b['date'] - $a['date'];
        });
    }
    
    return $files;
}

/**
 * Get database information
 */
function getDatabaseInfo() {
    if (!isDatabaseAvailable()) {
        return null;
    }
    
    try {
        $pdo = getDatabaseConnection();
        
        // Get database size
        $stmt = $pdo->query("
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
            FROM information_schema.tables 
            WHERE table_schema = '" . DB_NAME . "'
        ");
        $sizeResult = $stmt->fetch();
        
        // Get table count
        $stmt = $pdo->query("
            SELECT COUNT(*) as table_count 
            FROM information_schema.tables 
            WHERE table_schema = '" . DB_NAME . "'
        ");
        $tableResult = $stmt->fetch();
        
        return [
            'name' => DB_NAME,
            'size' => $sizeResult['size_mb'] ?? 0,
            'tables' => $tableResult['table_count'] ?? 0
        ];
        
    } catch (Exception $e) {
        return null;
    }
}

/**
 * Format bytes to human readable
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
            <h1 class="h2 text-white">
                <i class="fas fa-database me-2"></i>Database Backup & Restore
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <button class="btn btn-cyan" data-bs-toggle="modal" data-bs-target="#backupModal">
                    <i class="fas fa-plus me-1"></i>Create Backup
                </button>
            </div>
        </div>

        <?php if ($success): ?>
        <div class="alert alert-success bg-dark-grey-2 border-success text-success">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <!-- Database Information -->
        <div class="row mb-4">
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-body text-center">
                        <i class="fas fa-database fa-3x text-cyan mb-3"></i>
                        <h5 class="text-cyan"><?php echo $dbInfo ? $dbInfo['name'] : 'N/A'; ?></h5>
                        <p class="text-off-white mb-0">Database Name</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card bg-dark-grey-1 border-magenta">
                    <div class="card-body text-center">
                        <i class="fas fa-table fa-3x text-magenta mb-3"></i>
                        <h5 class="text-magenta"><?php echo $dbInfo ? $dbInfo['tables'] : '0'; ?></h5>
                        <p class="text-off-white mb-0">Tables</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6 mb-3">
                <div class="card bg-dark-grey-1 border-yellow">
                    <div class="card-body text-center">
                        <i class="fas fa-hdd fa-3x text-yellow mb-3"></i>
                        <h5 class="text-yellow"><?php echo $dbInfo ? $dbInfo['size'] . ' MB' : 'N/A'; ?></h5>
                        <p class="text-off-white mb-0">Database Size</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backup Files -->
        <div class="card bg-dark-grey-1 border-cyan">
            <div class="card-header bg-dark-grey-2 border-cyan">
                <h5 class="mb-0 text-cyan">
                    <i class="fas fa-archive me-2"></i>Backup Files
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($backupFiles)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-archive fa-3x text-muted mb-3"></i>
                    <p class="text-muted">No backup files found. Create your first backup to get started.</p>
                </div>
                <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-dark table-striped">
                        <thead>
                            <tr>
                                <th class="text-cyan">Filename</th>
                                <th class="text-cyan">Size</th>
                                <th class="text-cyan">Created</th>
                                <th class="text-cyan">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($backupFiles as $file): ?>
                            <tr>
                                <td class="text-white">
                                    <i class="fas fa-file-archive me-2 text-yellow"></i>
                                    <?php echo htmlspecialchars($file['name']); ?>
                                </td>
                                <td class="text-off-white"><?php echo formatBytes($file['size']); ?></td>
                                <td class="text-off-white"><?php echo date('Y-m-d H:i:s', $file['date']); ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                            <input type="hidden" name="action" value="download_backup">
                                            <input type="hidden" name="backup_file" value="<?php echo htmlspecialchars($file['name']); ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-cyan" title="Download">
                                                <i class="fas fa-download"></i>
                                            </button>
                                        </form>
                                        <button class="btn btn-sm btn-outline-success ms-1"
                                                onclick="confirmRestore('<?php echo htmlspecialchars($file['name']); ?>')"
                                                title="Restore">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger ms-1"
                                                onclick="confirmDelete('<?php echo htmlspecialchars($file['name']); ?>')"
                                                title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Create Backup Modal -->
<div class="modal fade" id="backupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content bg-dark-grey-1 border-cyan">
            <div class="modal-header border-dark-grey-3">
                <h5 class="modal-title text-cyan">
                    <i class="fas fa-plus me-2"></i>Create Database Backup
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" value="create_backup">

                    <div class="mb-3">
                        <label for="backup_name" class="form-label text-white">Backup Name</label>
                        <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                               id="backup_name" name="backup_name"
                               placeholder="Leave empty for auto-generated name">
                        <small class="text-muted">Format: backup_YYYY-MM-DD_HH-MM-SS</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="include_data" name="include_data" checked>
                            <label class="form-check-label text-white" for="include_data">
                                Include table data (recommended)
                            </label>
                        </div>
                        <small class="text-muted">Uncheck to backup structure only</small>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="compress" name="compress" checked>
                            <label class="form-check-label text-white" for="compress">
                                Compress backup file (.gz)
                            </label>
                        </div>
                        <small class="text-muted">Reduces file size significantly</small>
                    </div>
                </div>
                <div class="modal-footer border-dark-grey-3">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>Create Backup
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Hidden forms for restore and delete -->
<form id="restoreForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" value="restore_backup">
    <input type="hidden" name="backup_file" id="restoreFile">
</form>

<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="action" value="delete_backup">
    <input type="hidden" name="backup_file" id="deleteFile">
</form>

<script>
function confirmRestore(filename) {
    if (confirm('⚠️ WARNING: This will replace ALL current data with the backup data.\n\nThis action cannot be undone. Are you sure you want to restore from "' + filename + '"?')) {
        document.getElementById('restoreFile').value = filename;
        document.getElementById('restoreForm').submit();
    }
}

function confirmDelete(filename) {
    if (confirm('Are you sure you want to delete the backup file "' + filename + '"?\n\nThis action cannot be undone.')) {
        document.getElementById('deleteFile').value = filename;
        document.getElementById('deleteForm').submit();
    }
}
</script>

</body>
</html>
