<?php
/**
 * CYPTSHOP Admin Profile Management
 * Real database integration for admin profile updates
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

$success = '';
$error = '';

// Get current user
$currentUser = getCurrentUser();
if (!$currentUser) {
    header('Location: ' . SITE_URL . '/admin/login.php');
    exit;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'update_profile') {
            // Validate input data
            $name = trim($_POST['name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $username = trim($_POST['username'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $bio = trim($_POST['bio'] ?? '');
            
            // Validation
            if (empty($name) || empty($email) || empty($username)) {
                $error = 'Name, email, and username are required.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error = 'Please enter a valid email address.';
            } elseif (strlen($username) < 3) {
                $error = 'Username must be at least 3 characters long.';
            } else {
                // Check for duplicate username/email (excluding current user)
                $existingUser = getUserByUsername($username);
                $existingEmail = getUserByEmail($email);
                
                if ($existingUser && $existingUser['id'] != $currentUser['id']) {
                    $error = 'Username already exists.';
                } elseif ($existingEmail && $existingEmail['id'] != $currentUser['id']) {
                    $error = 'Email address already exists.';
                } else {
                    // Update profile data
                    $updateData = [
                        'name' => $name,
                        'email' => $email,
                        'username' => $username,
                        'phone' => $phone ?: null,
                        'bio' => $bio ?: null
                    ];
                    
                    if (updateUser($currentUser['id'], $updateData)) {
                        $success = 'Profile updated successfully!';
                        // Refresh current user data
                        $currentUser = getUserById($currentUser['id']);
                    } else {
                        $error = 'Failed to update profile. Please try again.';
                    }
                }
            }
        } elseif ($action === 'change_password') {
            $currentPassword = $_POST['current_password'] ?? '';
            $newPassword = $_POST['new_password'] ?? '';
            $confirmPassword = $_POST['confirm_password'] ?? '';
            
            // Validation
            if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
                $error = 'All password fields are required.';
            } elseif (strlen($newPassword) < 6) {
                $error = 'New password must be at least 6 characters long.';
            } elseif ($newPassword !== $confirmPassword) {
                $error = 'New passwords do not match.';
            } elseif (!verifyPassword($currentPassword, $currentUser['password'])) {
                $error = 'Current password is incorrect.';
            } else {
                // Update password
                $updateData = ['password' => hashPassword($newPassword)];
                
                if (updateUser($currentUser['id'], $updateData)) {
                    $success = 'Password changed successfully!';
                } else {
                    $error = 'Failed to change password. Please try again.';
                }
            }
        } elseif ($action === 'update_preferences') {
            $emailNotifications = isset($_POST['email_notifications']);
            $darkMode = isset($_POST['dark_mode']);
            $language = $_POST['language'] ?? 'en';
            $timezone = $_POST['timezone'] ?? 'America/New_York';
            
            // Update preferences (in a real implementation, this would be stored in a preferences table)
            $preferences = [
                'email_notifications' => $emailNotifications,
                'dark_mode' => $darkMode,
                'language' => $language,
                'timezone' => $timezone
            ];
            
            // For now, we'll store preferences as JSON in a preferences field
            $updateData = ['preferences' => json_encode($preferences)];
            
            if (updateUser($currentUser['id'], $updateData)) {
                $success = 'Preferences updated successfully!';
                $currentUser = getUserById($currentUser['id']);
            } else {
                $error = 'Failed to update preferences. Please try again.';
            }
        }
    }
}

// Get user preferences
$preferences = [];
if (!empty($currentUser['preferences'])) {
    $preferences = json_decode($currentUser['preferences'], true) ?: [];
}

$pageTitle = 'My Profile - Admin';
$bodyClass = 'admin-profile';

include BASE_PATH . 'includes/header.php';
?>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
            <h1 class="h2 text-white">
                <i class="fas fa-user-circle me-2"></i>My Profile
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="text-muted">
                    <i class="fas fa-clock me-1"></i>
                    Last login: <?php echo $currentUser['last_login'] ? date('M j, Y g:i A', strtotime($currentUser['last_login'])) : 'Never'; ?>
                </div>
            </div>
        </div>

        <?php if ($success): ?>
        <div class="alert alert-success bg-dark-grey-2 border-success text-success">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
        </div>
        <?php endif; ?>

        <?php if ($error): ?>
        <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Information -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card bg-dark-grey-1 border-cyan mb-4">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-user me-2"></i>Basic Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="update_profile">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label text-white">Full Name</label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="name" name="name" 
                                           value="<?php echo htmlspecialchars($currentUser['name'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="username" class="form-label text-white">Username</label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="username" name="username" 
                                           value="<?php echo htmlspecialchars($currentUser['username'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label text-white">Email Address</label>
                                    <input type="email" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="email" name="email" 
                                           value="<?php echo htmlspecialchars($currentUser['email'] ?? ''); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label text-white">Phone Number</label>
                                    <input type="tel" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($currentUser['phone'] ?? ''); ?>">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bio" class="form-label text-white">Bio</label>
                                <textarea class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                          id="bio" name="bio" rows="3" 
                                          placeholder="Tell us about yourself..."><?php echo htmlspecialchars($currentUser['bio'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>Update Profile
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Change Password -->
                <div class="card bg-dark-grey-1 border-yellow mb-4">
                    <div class="card-header bg-dark-grey-2 border-yellow">
                        <h5 class="mb-0 text-yellow">
                            <i class="fas fa-lock me-2"></i>Change Password
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="change_password">
                            
                            <div class="mb-3">
                                <label for="current_password" class="form-label text-white">Current Password</label>
                                <input type="password" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                       id="current_password" name="current_password" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="new_password" class="form-label text-white">New Password</label>
                                    <input type="password" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="new_password" name="new_password" required>
                                    <small class="text-muted">Minimum 6 characters</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label text-white">Confirm New Password</label>
                                    <input type="password" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-key me-1"></i>Change Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Profile Sidebar -->
            <div class="col-lg-4">
                <!-- Profile Summary -->
                <div class="card bg-dark-grey-1 border-magenta mb-4">
                    <div class="card-header bg-dark-grey-2 border-magenta">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-id-card me-2"></i>Profile Summary
                        </h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="profile-avatar mb-3">
                            <i class="fas fa-user-circle fa-5x text-cyan"></i>
                        </div>
                        <h5 class="text-white"><?php echo htmlspecialchars($currentUser['name'] ?? 'Admin User'); ?></h5>
                        <p class="text-muted mb-2"><?php echo htmlspecialchars($currentUser['email'] ?? ''); ?></p>
                        <span class="badge bg-<?php echo $currentUser['role'] === 'admin' ? 'cyan' : 'secondary'; ?>">
                            <?php echo ucfirst($currentUser['role'] ?? 'admin'); ?>
                        </span>
                        
                        <div class="mt-3 pt-3 border-top border-dark-grey-3">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="text-cyan h5 mb-0"><?php echo date('M Y', strtotime($currentUser['created_at'] ?? 'now')); ?></div>
                                    <small class="text-muted">Member Since</small>
                                </div>
                                <div class="col-6">
                                    <div class="text-magenta h5 mb-0"><?php echo $currentUser['active'] ? 'Active' : 'Inactive'; ?></div>
                                    <small class="text-muted">Status</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="card bg-dark-grey-1 border-yellow">
                    <div class="card-header bg-dark-grey-2 border-yellow">
                        <h5 class="mb-0 text-yellow">
                            <i class="fas fa-cog me-2"></i>Preferences
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="update_preferences">

                            <div class="mb-3">
                                <label for="timezone" class="form-label text-white">Timezone</label>
                                <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="timezone" name="timezone">
                                    <option value="America/New_York" <?php echo ($preferences['timezone'] ?? 'America/New_York') === 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                    <option value="America/Chicago" <?php echo ($preferences['timezone'] ?? '') === 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                    <option value="America/Denver" <?php echo ($preferences['timezone'] ?? '') === 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                    <option value="America/Los_Angeles" <?php echo ($preferences['timezone'] ?? '') === 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                    <option value="UTC" <?php echo ($preferences['timezone'] ?? '') === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="language" class="form-label text-white">Language</label>
                                <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="language" name="language">
                                    <option value="en" <?php echo ($preferences['language'] ?? 'en') === 'en' ? 'selected' : ''; ?>>English</option>
                                    <option value="es" <?php echo ($preferences['language'] ?? '') === 'es' ? 'selected' : ''; ?>>Spanish</option>
                                    <option value="fr" <?php echo ($preferences['language'] ?? '') === 'fr' ? 'selected' : ''; ?>>French</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications"
                                           <?php echo ($preferences['email_notifications'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label text-white" for="email_notifications">
                                        Email Notifications
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="dark_mode" name="dark_mode"
                                           <?php echo ($preferences['dark_mode'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label text-white" for="dark_mode">
                                        Dark Mode (Default)
                                    </label>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-1"></i>Update Preferences
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;

    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});

// Form validation feedback
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>

</body>
</html>
