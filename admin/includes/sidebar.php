<?php
/**
 * CYPTSHOP Unified Admin Sidebar
 * Consistent navigation across all admin sections
 */

// Get current page for active state
$currentPage = basename($_SERVER['PHP_SELF']);
$currentPath = $_SERVER['REQUEST_URI'];

// Define navigation items with their properties
$navItems = [
    'main' => [
        'title' => 'Main',
        'items' => [
            [
                'name' => 'Dashboard',
                'url' => '/admin/',
                'icon' => 'fas fa-tachometer-alt',
                'active' => $currentPage === 'index.php',
                'badge' => null
            ],
            [
                'name' => 'Products',
                'url' => '/admin/products.php',
                'icon' => 'fas fa-box',
                'active' => $currentPage === 'products.php',
                'badge' => null
            ],
            [
                'name' => 'Categories',
                'url' => '/admin/categories.php',
                'icon' => 'fas fa-tags',
                'active' => $currentPage === 'categories.php',
                'badge' => null
            ],
            [
                'name' => 'Orders',
                'url' => '/admin/orders.php',
                'icon' => 'fas fa-shopping-cart',
                'active' => $currentPage === 'orders.php',
                'badge' => null
            ]
        ]
    ],
    'marketing' => [
        'title' => 'Marketing',
        'items' => [
            [
                'name' => 'Coupons',
                'url' => '/admin/coupons.php',
                'icon' => 'fas fa-ticket-alt',
                'active' => $currentPage === 'coupons.php',
                'badge' => null
            ],
            [
                'name' => 'Portfolio',
                'url' => '/admin/portfolio.php',
                'icon' => 'fas fa-images',
                'active' => $currentPage === 'portfolio.php',
                'badge' => null
            ],
            [
                'name' => 'Services',
                'url' => '/admin/services.php',
                'icon' => 'fas fa-concierge-bell',
                'active' => $currentPage === 'services.php',
                'badge' => null
            ]
        ]
    ],
    'content' => [
        'title' => 'Content',
        'items' => [
            [
                'name' => 'Hero Content',
                'url' => '/admin/hero.php',
                'icon' => 'fas fa-image',
                'active' => $currentPage === 'hero.php',
                'badge' => null
            ],
            [
                'name' => 'Contacts',
                'url' => '/admin/contacts.php',
                'icon' => 'fas fa-envelope',
                'active' => $currentPage === 'contacts.php',
                'badge' => null
            ]
        ]
    ],
    'system' => [
        'title' => 'System',
        'items' => [
            [
                'name' => 'Users',
                'url' => '/admin/users.php',
                'icon' => 'fas fa-users',
                'active' => $currentPage === 'users.php',
                'badge' => null
            ],
            [
                'name' => 'Settings',
                'url' => '/admin/settings.php',
                'icon' => 'fas fa-cog',
                'active' => $currentPage === 'settings.php',
                'badge' => null
            ],
            [
                'name' => 'Themes',
                'url' => '/admin/themes.php',
                'icon' => 'fas fa-palette',
                'active' => $currentPage === 'themes.php',
                'badge' => null
            ]
        ]
    ]
];

// Try to get dynamic badge counts
try {
    if (isset($pdo)) {
        // Get pending orders count
        $stmt = $pdo->query("SELECT COUNT(*) FROM orders WHERE status = 'pending'");
        $pendingOrders = $stmt->fetchColumn();
        if ($pendingOrders > 0) {
            foreach ($navItems['main']['items'] as &$item) {
                if ($item['name'] === 'Orders') {
                    $item['badge'] = $pendingOrders;
                    break;
                }
            }
        }
        
        // Get active coupons count
        $stmt = $pdo->query("SELECT COUNT(*) FROM coupons WHERE status = 'active'");
        $activeCoupons = $stmt->fetchColumn();
        if ($activeCoupons > 0) {
            foreach ($navItems['marketing']['items'] as &$item) {
                if ($item['name'] === 'Coupons') {
                    $item['badge'] = $activeCoupons;
                    break;
                }
            }
        }
    }
} catch (Exception $e) {
    // Silently handle database errors
}
?>

<!-- Sidebar Toggle Button -->
<button class="btn btn-dark d-md-none sidebar-toggle" type="button" data-bs-toggle="offcanvas" data-bs-target="#adminSidebar" aria-controls="adminSidebar">
    <i class="fas fa-bars"></i>
</button>

<!-- Desktop Sidebar Toggle -->
<button class="btn btn-outline-cyan btn-sm sidebar-toggle-desktop d-none d-md-block" type="button" onclick="toggleSidebar()">
    <i class="fas fa-bars" id="sidebarToggleIcon"></i>
</button>

<!-- Unified Admin Sidebar -->
<nav class="offcanvas-md offcanvas-start bg-dark-grey-1 sidebar" tabindex="-1" id="adminSidebar" style="width: 280px;">
    <div class="offcanvas-header d-md-none">
        <h5 class="offcanvas-title text-cyan">
            <i class="fas fa-tshirt me-2"></i>CYPTSHOP Admin
        </h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" data-bs-target="#adminSidebar"></button>
    </div>
    <div class="offcanvas-body p-0">
        <div class="position-sticky pt-3">
        <!-- Admin Brand -->
        <div class="sidebar-brand px-3 mb-4">
            <a href="<?php echo SITE_URL; ?>/admin/" class="text-decoration-none">
                <h4 class="text-cyan mb-0">
                    <i class="fas fa-tshirt me-2"></i>
                    CYPTSHOP
                </h4>
                <small class="text-off-white">Admin Panel</small>
            </a>
        </div>

        <!-- Navigation Sections -->
        <?php foreach ($navItems as $sectionKey => $section): ?>
            <div class="sidebar-section mb-3">
                <h6 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-uppercase">
                    <span class="text-cyan"><?php echo $section['title']; ?></span>
                </h6>
                
                <ul class="nav flex-column">
                    <?php foreach ($section['items'] as $item): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo $item['active'] ? 'active text-white bg-dark-grey-2' : 'text-off-white'; ?> d-flex align-items-center" 
                               href="<?php echo SITE_URL . $item['url']; ?>">
                                <i class="<?php echo $item['icon']; ?> me-2"></i>
                                <span class="flex-grow-1"><?php echo $item['name']; ?></span>
                                <?php if ($item['badge']): ?>
                                    <span class="badge bg-danger rounded-pill"><?php echo $item['badge']; ?></span>
                                <?php endif; ?>
                            </a>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endforeach; ?>

        <!-- Divider -->
        <hr class="border-dark-grey-3 mx-3">

        <!-- Quick Actions -->
        <div class="sidebar-section">
            <h6 class="sidebar-heading px-3 mb-2 text-uppercase">
                <span class="text-cyan">Quick Actions</span>
            </h6>
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>View Website
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/backup.php">
                        <i class="fas fa-download me-2"></i>Backup Data
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/analytics.php">
                        <i class="fas fa-chart-bar me-2"></i>Analytics
                    </a>
                </li>
            </ul>
        </div>

        <!-- Divider -->
        <hr class="border-dark-grey-3 mx-3">

        <!-- User Actions -->
        <div class="sidebar-section">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link text-off-white" href="<?php echo SITE_URL; ?>/admin/profile.php">
                        <i class="fas fa-user-circle me-2"></i>Profile
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-danger" href="<?php echo SITE_URL; ?>/admin/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </li>
            </ul>
        </div>

        <!-- Admin Info -->
        <div class="sidebar-footer px-3 py-2 mt-4">
            <small class="text-off-white d-block">
                <i class="fas fa-user me-1"></i>
                <?php echo htmlspecialchars($_SESSION['username'] ?? 'Admin'); ?>
            </small>
            <small class="text-off-white d-block">
                <i class="fas fa-clock me-1"></i>
                <?php echo date('M j, Y g:i A'); ?>
            </small>
        </div>
    </div>
</nav>

<!-- Sidebar CSS -->
<style>
/* Sidebar Toggle Buttons */
.sidebar-toggle {
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1050;
    border-radius: 8px;
}

.sidebar-toggle-desktop {
    position: fixed;
    top: 15px;
    left: 15px;
    z-index: 1050;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.sidebar-toggle-desktop.sidebar-open {
    left: 295px;
}

/* Sidebar Styles */
.sidebar {
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    overflow-y: auto;
    transition: all 0.3s ease;
    z-index: 1040;
}

.sidebar.collapsed {
    margin-left: -280px;
}

.sidebar-brand a:hover {
    text-decoration: none !important;
}

.sidebar .nav-link {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    transition: all 0.2s ease;
}

.sidebar .nav-link:hover {
    background-color: var(--admin-dark, #2c3034);
    color: var(--admin-primary, #00D4FF) !important;
    transform: translateX(2px);
}

.sidebar .nav-link.active {
    background-color: var(--admin-dark, #2c3034) !important;
    color: var(--admin-primary, #00D4FF) !important;
    border-left: 3px solid var(--admin-primary, #00D4FF);
}

.sidebar-heading {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.sidebar-section {
    margin-bottom: 1rem;
}

.sidebar-footer {
    border-top: 1px solid var(--admin-border, #495057);
    background-color: rgba(0,0,0,0.1);
}

/* Main Content Adjustments */
.main-content {
    transition: margin-left 0.3s ease;
    margin-left: 280px;
}

.main-content.sidebar-collapsed {
    margin-left: 0;
}

/* Mobile responsiveness */
@media (max-width: 767.98px) {
    .main-content {
        margin-left: 0 !important;
    }

    .sidebar-toggle-desktop {
        display: none !important;
    }
}

/* Desktop specific */
@media (min-width: 768px) {
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 280px;
    }
}

/* Scrollbar styling */
.sidebar::-webkit-scrollbar {
    width: 4px;
}

.sidebar::-webkit-scrollbar-track {
    background: var(--admin-darker, #1a1e21);
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--admin-border, #495057);
    border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--admin-primary, #00D4FF);
}

/* Overlay for mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 1030;
    display: none;
}

.sidebar-overlay.show {
    display: block;
}
</style>

<!-- Sidebar JavaScript -->
<script>
// Sidebar state management
let sidebarOpen = true;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar state from localStorage
    const savedState = localStorage.getItem('sidebarOpen');
    if (savedState !== null) {
        sidebarOpen = savedState === 'true';
        applySidebarState();
    }

    // Add active state management
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');

    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href.split('/').pop())) {
            link.classList.add('active');
            link.classList.remove('text-off-white');
            link.classList.add('text-white');
        }
    });

    // Add smooth scrolling for sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        sidebar.style.scrollBehavior = 'smooth';
    }

    // Add keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Alt + D for Dashboard
        if (e.altKey && e.key === 'd') {
            e.preventDefault();
            window.location.href = '<?php echo SITE_URL; ?>/admin/';
        }

        // Alt + P for Products
        if (e.altKey && e.key === 'p') {
            e.preventDefault();
            window.location.href = '<?php echo SITE_URL; ?>/admin/products.php';
        }

        // Alt + C for Coupons
        if (e.altKey && e.key === 'c') {
            e.preventDefault();
            window.location.href = '<?php echo SITE_URL; ?>/admin/coupons.php';
        }

        // Ctrl + B to toggle sidebar
        if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
            e.preventDefault();
            toggleSidebar();
        }
    });
});

/**
 * Toggle sidebar open/closed state
 */
function toggleSidebar() {
    sidebarOpen = !sidebarOpen;
    applySidebarState();
    localStorage.setItem('sidebarOpen', sidebarOpen.toString());
}

/**
 * Apply current sidebar state to UI
 */
function applySidebarState() {
    const sidebar = document.getElementById('adminSidebar');
    const mainContent = document.querySelector('.main-content');
    const toggleBtn = document.querySelector('.sidebar-toggle-desktop');
    const toggleIcon = document.getElementById('sidebarToggleIcon');

    if (window.innerWidth >= 768) { // Desktop only
        if (sidebarOpen) {
            sidebar.classList.remove('collapsed');
            mainContent.classList.remove('sidebar-collapsed');
            toggleBtn.classList.add('sidebar-open');
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-times';
            }
        } else {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('sidebar-collapsed');
            toggleBtn.classList.remove('sidebar-open');
            if (toggleIcon) {
                toggleIcon.className = 'fas fa-bars';
            }
        }
    }
}

/**
 * Handle window resize
 */
window.addEventListener('resize', function() {
    if (window.innerWidth < 768) {
        // Mobile: reset sidebar state
        const sidebar = document.getElementById('adminSidebar');
        const mainContent = document.querySelector('.main-content');
        sidebar.classList.remove('collapsed');
        mainContent.classList.remove('sidebar-collapsed');
    } else {
        // Desktop: apply saved state
        applySidebarState();
    }
});

/**
 * Auto-close sidebar on mobile when clicking nav links
 */
document.addEventListener('click', function(e) {
    if (e.target.closest('.sidebar .nav-link') && window.innerWidth < 768) {
        const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('adminSidebar'));
        if (offcanvas) {
            offcanvas.hide();
        }
    }
});

/**
 * Show notification for keyboard shortcuts
 */
function showKeyboardShortcuts() {
    const shortcuts = [
        'Ctrl+B: Toggle Sidebar',
        'Alt+D: Dashboard',
        'Alt+P: Products',
        'Alt+C: Coupons',
        'Esc: Close Modals'
    ];

    if (typeof ajax !== 'undefined') {
        ajax.showNotification('Keyboard Shortcuts:<br>' + shortcuts.join('<br>'), 'info', 8000);
    } else {
        alert('Keyboard Shortcuts:\n' + shortcuts.join('\n'));
    }
}

// Add keyboard shortcut help
document.addEventListener('keydown', function(e) {
    if (e.key === 'F1' || (e.ctrlKey && e.key === '?')) {
        e.preventDefault();
        showKeyboardShortcuts();
    }
});
</script>
