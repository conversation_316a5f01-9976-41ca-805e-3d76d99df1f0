<?php
/**
 * CYPTSHOP Admin Settings
 * Comprehensive settings management for the admin dashboard
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $response = ['success' => false, 'message' => ''];
    
    switch ($_POST['action']) {
        case 'save_general':
            // Save general settings
            $settings = [
                'site_name' => $_POST['site_name'] ?? 'CYPTSHOP',
                'site_description' => $_POST['site_description'] ?? '',
                'admin_email' => $_POST['admin_email'] ?? '',
                'timezone' => $_POST['timezone'] ?? 'America/New_York',
                'currency' => $_POST['currency'] ?? 'USD',
                'currency_symbol' => $_POST['currency_symbol'] ?? '$'
            ];
            
            $saved = 0;
            foreach ($settings as $key => $value) {
                if (setThemeSetting($key, $value, 'text', 'general')) {
                    $saved++;
                }
            }
            
            if ($saved === count($settings)) {
                $successMessage = 'General settings saved successfully!';
            } else {
                $errorMessage = 'Failed to save some settings.';
            }
            break;
            
        case 'save_email':
            // Save email settings
            $emailSettings = [
                'smtp_host' => $_POST['smtp_host'] ?? '',
                'smtp_port' => $_POST['smtp_port'] ?? '587',
                'smtp_username' => $_POST['smtp_username'] ?? '',
                'smtp_password' => $_POST['smtp_password'] ?? '',
                'smtp_encryption' => $_POST['smtp_encryption'] ?? 'tls',
                'from_email' => $_POST['from_email'] ?? '',
                'from_name' => $_POST['from_name'] ?? 'CYPTSHOP'
            ];
            
            $saved = 0;
            foreach ($emailSettings as $key => $value) {
                if (setThemeSetting($key, $value, 'text', 'email')) {
                    $saved++;
                }
            }
            
            if ($saved === count($emailSettings)) {
                $successMessage = 'Email settings saved successfully!';
            } else {
                $errorMessage = 'Failed to save some email settings.';
            }
            break;
            
        case 'save_security':
            // Save security settings
            $securitySettings = [
                'enable_2fa' => isset($_POST['enable_2fa']) ? '1' : '0',
                'session_timeout' => $_POST['session_timeout'] ?? '3600',
                'max_login_attempts' => $_POST['max_login_attempts'] ?? '5',
                'lockout_duration' => $_POST['lockout_duration'] ?? '900',
                'require_strong_passwords' => isset($_POST['require_strong_passwords']) ? '1' : '0',
                'enable_ip_whitelist' => isset($_POST['enable_ip_whitelist']) ? '1' : '0'
            ];
            
            $saved = 0;
            foreach ($securitySettings as $key => $value) {
                if (setThemeSetting($key, $value, 'text', 'security')) {
                    $saved++;
                }
            }
            
            if ($saved === count($securitySettings)) {
                $successMessage = 'Security settings saved successfully!';
            } else {
                $errorMessage = 'Failed to save some security settings.';
            }
            break;
    }
}

// Get current settings
$generalSettings = [
    'site_name' => getThemeSetting('site_name') ?? 'CYPTSHOP',
    'site_description' => getThemeSetting('site_description') ?? 'Professional T-Shirt Printing & Design',
    'admin_email' => getThemeSetting('admin_email') ?? '<EMAIL>',
    'timezone' => getThemeSetting('timezone') ?? 'America/New_York',
    'currency' => getThemeSetting('currency') ?? 'USD',
    'currency_symbol' => getThemeSetting('currency_symbol') ?? '$'
];

$emailSettings = [
    'smtp_host' => getThemeSetting('smtp_host') ?? '',
    'smtp_port' => getThemeSetting('smtp_port') ?? '587',
    'smtp_username' => getThemeSetting('smtp_username') ?? '',
    'smtp_password' => getThemeSetting('smtp_password') ?? '',
    'smtp_encryption' => getThemeSetting('smtp_encryption') ?? 'tls',
    'from_email' => getThemeSetting('from_email') ?? '',
    'from_name' => getThemeSetting('from_name') ?? 'CYPTSHOP'
];

$securitySettings = [
    'enable_2fa' => getThemeSetting('enable_2fa') ?? '0',
    'session_timeout' => getThemeSetting('session_timeout') ?? '3600',
    'max_login_attempts' => getThemeSetting('max_login_attempts') ?? '5',
    'lockout_duration' => getThemeSetting('lockout_duration') ?? '900',
    'require_strong_passwords' => getThemeSetting('require_strong_passwords') ?? '0',
    'enable_ip_whitelist' => getThemeSetting('enable_ip_whitelist') ?? '0'
];

$pageTitle = 'Settings - Admin';
$bodyClass = 'admin-settings';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Enhanced dark mode text readability for settings page */
.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.text-white-60 {
    color: rgba(255, 255, 255, 0.6) !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* Better form styling for dark mode */
.form-control, .form-select {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
    color: #ffffff !important;
}

.form-control:focus, .form-select:focus {
    background-color: #404040 !important;
    border-color: #00FFFF !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 255, 255, 0.25) !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Form labels and text */
.form-label {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
}

.form-check-label {
    color: rgba(255, 255, 255, 0.9) !important;
}

small.text-muted {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Button improvements */
.btn-outline-secondary {
    color: rgba(255, 255, 255, 0.8) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
}

.btn-outline-secondary:hover {
    color: #000000 !important;
    background-color: rgba(255, 255, 255, 0.9) !important;
    border-color: rgba(255, 255, 255, 0.9) !important;
}

/* Enhanced tab styling for better readability */
.nav-tabs-dark .nav-link {
    background-color: #2d2d2d !important;
    border: 1px solid #404040 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    margin-right: 2px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-tabs-dark .nav-link:hover {
    background-color: #404040 !important;
    color: #00FFFF !important;
    border-color: #00FFFF !important;
}

.nav-tabs-dark .nav-link.active {
    background-color: #00FFFF !important;
    color: #000000 !important;
    border-color: #00FFFF !important;
    font-weight: 600;
}

.nav-tabs-dark .nav-link i {
    margin-right: 8px;
}

/* Tab content improvements */
.tab-content {
    background-color: transparent;
}

.tab-pane {
    display: none;
}

.tab-pane.show.active {
    display: block;
}

/* Card improvements */
.card-body h6 {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 600;
}

.card-body .small {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Theme preview improvements */
.theme-preview-mini {
    background-color: #1a1a1a !important;
    border: 1px solid #404040;
}

.theme-preview-mini .small {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Alert improvements */
.alert-success {
    background-color: rgba(25, 135, 84, 0.1) !important;
    border-color: rgba(25, 135, 84, 0.3) !important;
    color: rgba(25, 135, 84, 0.9) !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    color: rgba(220, 53, 69, 0.9) !important;
}

/* Form check improvements */
.form-check-input:checked {
    background-color: #00FFFF;
    border-color: #00FFFF;
}

.form-check-input:focus {
    border-color: #00FFFF;
    box-shadow: 0 0 0 0.25rem rgba(0, 255, 255, 0.25);
}

/* Select dropdown improvements */
.form-select option {
    background-color: #2d2d2d;
    color: #ffffff;
}

/* Link improvements */
a.btn {
    text-decoration: none;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .nav-tabs-dark .nav-link {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }

    .nav-tabs-dark .nav-link i {
        margin-right: 4px;
    }
}

/* Additional text contrast improvements */
.card-header h5 {
    font-weight: 600;
}

.btn {
    font-weight: 500;
}

/* Improve visibility of disabled elements */
.form-control:disabled,
.form-select:disabled {
    background-color: #1a1a1a !important;
    color: rgba(255, 255, 255, 0.5) !important;
    border-color: #333333 !important;
}

/* Better spacing for form elements */
.form-label {
    margin-bottom: 0.5rem;
}

.form-check {
    margin-bottom: 0.5rem;
}

.form-check-label {
    margin-left: 0.25rem;
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
            <h1 class="h2 text-white">
                <i class="fas fa-cog me-2"></i>Settings
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <button class="btn btn-outline-secondary me-2" onclick="exportSettings()">
                    <i class="fas fa-download me-1"></i>Export Settings
                </button>
                <button class="btn btn-cyan" onclick="importSettings()">
                    <i class="fas fa-upload me-1"></i>Import Settings
                </button>
            </div>
        </div>

        <?php if (isset($successMessage)): ?>
        <div class="alert alert-success bg-dark-grey-2 border-success text-success">
            <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($successMessage); ?>
        </div>
        <?php endif; ?>

        <?php if (isset($errorMessage)): ?>
        <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo htmlspecialchars($errorMessage); ?>
        </div>
        <?php endif; ?>

        <!-- Settings Navigation Tabs -->
        <ul class="nav nav-tabs nav-tabs-dark mb-4" id="settingsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                    <i class="fas fa-cog me-2"></i>General
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                    <i class="fas fa-envelope me-2"></i>Email
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                    <i class="fas fa-shield-alt me-2"></i>Security
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="themes-tab" data-bs-toggle="tab" data-bs-target="#themes" type="button" role="tab">
                    <i class="fas fa-palette me-2"></i>Themes
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                    <i class="fas fa-tools me-2"></i>Advanced
                </button>
            </li>
        </ul>

        <!-- Settings Tab Content -->
        <div class="tab-content" id="settingsTabContent">
            <!-- General Settings -->
            <div class="tab-pane fade show active" id="general" role="tabpanel">
                <div class="card bg-dark-grey-1 border-cyan">
                    <div class="card-header bg-dark-grey-2 border-cyan">
                        <h5 class="mb-0 text-cyan">
                            <i class="fas fa-cog me-2"></i>General Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="generalForm">
                            <input type="hidden" name="action" value="save_general">
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="site_name" class="form-label text-white">Site Name</label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="site_name" name="site_name" 
                                           value="<?php echo htmlspecialchars($generalSettings['site_name']); ?>" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="admin_email" class="form-label text-white">Admin Email</label>
                                    <input type="email" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="admin_email" name="admin_email" 
                                           value="<?php echo htmlspecialchars($generalSettings['admin_email']); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="site_description" class="form-label text-white">Site Description</label>
                                <textarea class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                          id="site_description" name="site_description" rows="3"><?php echo htmlspecialchars($generalSettings['site_description']); ?></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="timezone" class="form-label text-white">Timezone</label>
                                    <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="timezone" name="timezone">
                                        <option value="America/New_York" <?php echo $generalSettings['timezone'] === 'America/New_York' ? 'selected' : ''; ?>>Eastern Time</option>
                                        <option value="America/Chicago" <?php echo $generalSettings['timezone'] === 'America/Chicago' ? 'selected' : ''; ?>>Central Time</option>
                                        <option value="America/Denver" <?php echo $generalSettings['timezone'] === 'America/Denver' ? 'selected' : ''; ?>>Mountain Time</option>
                                        <option value="America/Los_Angeles" <?php echo $generalSettings['timezone'] === 'America/Los_Angeles' ? 'selected' : ''; ?>>Pacific Time</option>
                                        <option value="UTC" <?php echo $generalSettings['timezone'] === 'UTC' ? 'selected' : ''; ?>>UTC</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="currency" class="form-label text-white">Currency</label>
                                    <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="currency" name="currency">
                                        <option value="USD" <?php echo $generalSettings['currency'] === 'USD' ? 'selected' : ''; ?>>USD - US Dollar</option>
                                        <option value="EUR" <?php echo $generalSettings['currency'] === 'EUR' ? 'selected' : ''; ?>>EUR - Euro</option>
                                        <option value="GBP" <?php echo $generalSettings['currency'] === 'GBP' ? 'selected' : ''; ?>>GBP - British Pound</option>
                                        <option value="CAD" <?php echo $generalSettings['currency'] === 'CAD' ? 'selected' : ''; ?>>CAD - Canadian Dollar</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="currency_symbol" class="form-label text-white">Currency Symbol</label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white" 
                                           id="currency_symbol" name="currency_symbol" 
                                           value="<?php echo htmlspecialchars($generalSettings['currency_symbol']); ?>" maxlength="3">
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-end">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>Save General Settings
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Email Settings -->
            <div class="tab-pane fade" id="email" role="tabpanel">
                <div class="card bg-dark-grey-1 border-magenta">
                    <div class="card-header bg-dark-grey-2 border-magenta">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-envelope me-2"></i>Email Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="emailForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="save_email">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="smtp_host" class="form-label text-white">SMTP Host</label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                           id="smtp_host" name="smtp_host"
                                           value="<?php echo htmlspecialchars($emailSettings['smtp_host']); ?>"
                                           placeholder="smtp.gmail.com">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="smtp_port" class="form-label text-white">SMTP Port</label>
                                    <input type="number" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                           id="smtp_port" name="smtp_port"
                                           value="<?php echo htmlspecialchars($emailSettings['smtp_port']); ?>"
                                           placeholder="587">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="smtp_username" class="form-label text-white">SMTP Username</label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                           id="smtp_username" name="smtp_username"
                                           value="<?php echo htmlspecialchars($emailSettings['smtp_username']); ?>"
                                           placeholder="<EMAIL>">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="smtp_password" class="form-label text-white">SMTP Password</label>
                                    <input type="password" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                           id="smtp_password" name="smtp_password"
                                           value="<?php echo htmlspecialchars($emailSettings['smtp_password']); ?>"
                                           placeholder="App Password">
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="smtp_encryption" class="form-label text-white">Encryption</label>
                                    <select class="form-select bg-dark-grey-2 border-dark-grey-3 text-white" id="smtp_encryption" name="smtp_encryption">
                                        <option value="tls" <?php echo ($emailSettings['smtp_encryption'] ?? 'tls') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                        <option value="ssl" <?php echo ($emailSettings['smtp_encryption'] ?? '') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                        <option value="none" <?php echo ($emailSettings['smtp_encryption'] ?? '') === 'none' ? 'selected' : ''; ?>>None</option>
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="from_email" class="form-label text-white">From Email</label>
                                    <input type="email" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                           id="from_email" name="from_email"
                                           value="<?php echo htmlspecialchars($emailSettings['from_email'] ?? ''); ?>"
                                           placeholder="<EMAIL>">
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="from_name" class="form-label text-white">From Name</label>
                                    <input type="text" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                           id="from_name" name="from_name"
                                           value="<?php echo htmlspecialchars($emailSettings['from_name'] ?? 'CYPTSHOP'); ?>"
                                           placeholder="CYPTSHOP">
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-outline-secondary" onclick="testEmailConnection()">
                                    <i class="fas fa-paper-plane me-1"></i>Test Connection
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>Save Email Settings
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="tab-pane fade" id="security" role="tabpanel">
                <div class="card bg-dark-grey-1 border-yellow">
                    <div class="card-header bg-dark-grey-2 border-yellow">
                        <h5 class="mb-0 text-yellow">
                            <i class="fas fa-shield-alt me-2"></i>Security Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="securityForm">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="save_security">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="session_timeout" class="form-label text-white">Session Timeout (seconds)</label>
                                    <input type="number" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                           id="session_timeout" name="session_timeout"
                                           value="<?php echo htmlspecialchars($securitySettings['session_timeout']); ?>"
                                           min="300" max="86400">
                                    <small class="text-muted">Default: 3600 (1 hour)</small>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="max_login_attempts" class="form-label text-white">Max Login Attempts</label>
                                    <input type="number" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                           id="max_login_attempts" name="max_login_attempts"
                                           value="<?php echo htmlspecialchars($securitySettings['max_login_attempts']); ?>"
                                           min="3" max="10">
                                    <small class="text-muted">Default: 5 attempts</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enable_2fa" name="enable_2fa"
                                               <?php echo $securitySettings['enable_2fa'] === '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label text-white" for="enable_2fa">
                                            Enable Two-Factor Authentication
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="require_strong_passwords" name="require_strong_passwords"
                                               <?php echo $securitySettings['require_strong_passwords'] === '1' ? 'checked' : ''; ?>>
                                        <label class="form-check-label text-white" for="require_strong_passwords">
                                            Require Strong Passwords
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="lockout_duration" class="form-label text-white">Lockout Duration (seconds)</label>
                                <input type="number" class="form-control bg-dark-grey-2 border-dark-grey-3 text-white"
                                       id="lockout_duration" name="lockout_duration"
                                       value="<?php echo htmlspecialchars($securitySettings['lockout_duration'] ?? '900'); ?>"
                                       min="300" max="3600">
                                <small class="text-muted">Default: 900 (15 minutes)</small>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enable_ip_whitelist" name="enable_ip_whitelist"
                                           <?php echo ($securitySettings['enable_ip_whitelist'] ?? '0') === '1' ? 'checked' : ''; ?>>
                                    <label class="form-check-label text-white" for="enable_ip_whitelist">
                                        Enable IP Whitelist for Admin Access
                                    </label>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="backup.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-database me-1"></i>Database Backup
                                </a>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save me-1"></i>Save Security Settings
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Themes Settings -->
            <div class="tab-pane fade" id="themes" role="tabpanel">
                <div class="card bg-dark-grey-1 border-magenta">
                    <div class="card-header bg-dark-grey-2 border-magenta">
                        <h5 class="mb-0 text-magenta">
                            <i class="fas fa-palette me-2"></i>Theme Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-white mb-3">Quick Theme Actions</h6>
                                <div class="d-grid gap-2">
                                    <a href="themes.php" class="btn btn-cyan">
                                        <i class="fas fa-palette me-2"></i>Manage Theme Colors
                                    </a>
                                    <button class="btn btn-outline-secondary" onclick="resetTheme()">
                                        <i class="fas fa-undo me-2"></i>Reset to Default Theme
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="exportTheme()">
                                        <i class="fas fa-download me-2"></i>Export Current Theme
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-white mb-3">Current Theme Preview</h6>
                                <div class="theme-preview-mini p-3 bg-dark-grey-2 rounded">
                                    <div class="d-flex mb-2">
                                        <div class="color-swatch me-2" style="background: #00FFFF; width: 20px; height: 20px; border-radius: 3px;"></div>
                                        <span class="text-cyan small">Primary (Cyan)</span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <div class="color-swatch me-2" style="background: #FF00FF; width: 20px; height: 20px; border-radius: 3px;"></div>
                                        <span class="text-magenta small">Secondary (Magenta)</span>
                                    </div>
                                    <div class="d-flex mb-2">
                                        <div class="color-swatch me-2" style="background: #FFFF00; width: 20px; height: 20px; border-radius: 3px;"></div>
                                        <span class="text-yellow small">Accent (Yellow)</span>
                                    </div>
                                    <div class="d-flex">
                                        <div class="color-swatch me-2" style="background: #000000; width: 20px; height: 20px; border-radius: 3px; border: 1px solid #333;"></div>
                                        <span class="text-white small">Background (Black)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Settings -->
            <div class="tab-pane fade" id="advanced" role="tabpanel">
                <div class="card bg-dark-grey-1 border-yellow">
                    <div class="card-header bg-dark-grey-2 border-yellow">
                        <h5 class="mb-0 text-yellow">
                            <i class="fas fa-tools me-2"></i>Advanced Settings
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <h6 class="text-white mb-3">System Maintenance</h6>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-warning" onclick="clearCache()">
                                        <i class="fas fa-broom me-2"></i>Clear Cache
                                    </button>
                                    <button class="btn btn-outline-info" onclick="optimizeDatabase()">
                                        <i class="fas fa-database me-2"></i>Optimize Database
                                    </button>
                                    <button class="btn btn-outline-success" onclick="backupDatabase()">
                                        <i class="fas fa-download me-2"></i>Backup Database
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <h6 class="text-white mb-3">System Information</h6>
                                <div class="system-info">
                                    <div class="info-item d-flex justify-content-between mb-2">
                                        <span class="text-muted">PHP Version:</span>
                                        <span class="text-white"><?php echo PHP_VERSION; ?></span>
                                    </div>
                                    <div class="info-item d-flex justify-content-between mb-2">
                                        <span class="text-muted">Server Software:</span>
                                        <span class="text-white"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?></span>
                                    </div>
                                    <div class="info-item d-flex justify-content-between mb-2">
                                        <span class="text-muted">Database:</span>
                                        <span class="text-white">MySQL</span>
                                    </div>
                                    <div class="info-item d-flex justify-content-between">
                                        <span class="text-muted">CYPTSHOP Version:</span>
                                        <span class="text-cyan">2.0.0</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-white mb-3">Debug & Logs</h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-secondary w-100" onclick="viewErrorLogs()">
                                            <i class="fas fa-bug me-2"></i>View Error Logs
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-secondary w-100" onclick="viewAccessLogs()">
                                            <i class="fas fa-eye me-2"></i>View Access Logs
                                        </button>
                                    </div>
                                    <div class="col-md-4">
                                        <button class="btn btn-outline-secondary w-100" onclick="downloadLogs()">
                                            <i class="fas fa-download me-2"></i>Download Logs
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Settings page JavaScript functions
function exportSettings() {
    // Export all settings to JSON file
    alert('Export functionality will be implemented');
}

function importSettings() {
    // Import settings from JSON file
    alert('Import functionality will be implemented');
}

function testEmailConnection() {
    // Test SMTP connection
    alert('Testing email connection...');
}

function resetTheme() {
    if (confirm('Reset theme to default CYPTSHOP colors?')) {
        window.location.href = 'themes.php?action=reset';
    }
}

function exportTheme() {
    window.location.href = 'themes.php?action=export';
}

function clearCache() {
    if (confirm('Clear all cached files?')) {
        alert('Cache cleared successfully');
    }
}

function optimizeDatabase() {
    if (confirm('Optimize database tables?')) {
        alert('Database optimization completed');
    }
}

function backupDatabase() {
    if (confirm('Create database backup?')) {
        alert('Database backup created');
    }
}

function viewErrorLogs() {
    window.open('logs.php?type=error', '_blank');
}

function viewAccessLogs() {
    window.open('logs.php?type=access', '_blank');
}

function downloadLogs() {
    window.location.href = 'logs.php?action=download';
}

// Simple tab system that works without Bootstrap
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing settings tabs...');

    // Get all tab buttons and panes
    const tabButtons = document.querySelectorAll('#settingsTabs .nav-link');
    const tabPanes = document.querySelectorAll('.tab-pane');

    console.log('Found', tabButtons.length, 'tab buttons and', tabPanes.length, 'tab panes');

    // Add click handlers to each tab button
    tabButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Tab clicked:', this.getAttribute('data-bs-target'));

            // Remove active class from all tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // Remove show and active from all panes
            tabPanes.forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // Add active to clicked tab
            this.classList.add('active');

            // Show corresponding pane
            const targetId = this.getAttribute('data-bs-target');
            if (targetId) {
                const targetPane = document.querySelector(targetId);
                if (targetPane) {
                    targetPane.classList.add('show', 'active');
                    console.log('Showing pane:', targetId);
                } else {
                    console.error('Target pane not found:', targetId);
                }
            }
        });
    });

    // Make sure the first tab is active by default
    if (tabButtons.length > 0) {
        tabButtons[0].click();
    }

    console.log('Settings tabs initialized successfully');
});
</script>

</body>
</html>
