<?php
/**
 * CYPTSHOP Services Management System
 * Complete service management with pricing and categories
 */

define('BASE_PATH', dirname(__DIR__) . '/');
require_once BASE_PATH . 'includes/auth.php';
require_once BASE_PATH . 'includes/database.php';

// Start session and require admin access
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
requireAdmin();

// Get database connection
$pdo = getDatabaseConnection();

// Initialize services in database if empty
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM services");
    $serviceCount = $stmt->fetchColumn();
    
    if ($serviceCount == 0) {
        // Insert default services
        $defaultServices = [
            [
                'name' => 'Custom T-Shirt Design',
                'description' => 'Professional custom t-shirt design service with unlimited revisions',
                'price' => 49.99,
                'image' => 'service-tshirt-design.jpg',
                'status' => 'active'
            ],
            [
                'name' => 'Logo Design',
                'description' => 'Custom logo design for your brand with multiple concepts',
                'price' => 99.99,
                'image' => 'service-logo-design.jpg',
                'status' => 'active'
            ],
            [
                'name' => 'Print Setup & Consultation',
                'description' => 'Professional print setup and consultation for your designs',
                'price' => 29.99,
                'image' => 'service-print-setup.jpg',
                'status' => 'active'
            ],
            [
                'name' => 'Rush Order Processing',
                'description' => 'Expedited processing for urgent orders (24-48 hours)',
                'price' => 25.00,
                'image' => 'service-rush-order.jpg',
                'status' => 'active'
            ]
        ];
        
        foreach ($defaultServices as $service) {
            $stmt = $pdo->prepare("
                INSERT INTO services (name, description, price, image, status)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $service['name'],
                $service['description'],
                $service['price'],
                $service['image'],
                $service['status']
            ]);
        }
    }
} catch (Exception $e) {
    error_log('Service initialization error: ' . $e->getMessage());
}

$success = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';

        switch ($action) {
            case 'add':
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $price = floatval($_POST['price'] ?? 0);
                $image = trim($_POST['image'] ?? '');
                $status = isset($_POST['active']) ? 'active' : 'inactive';

                if (empty($name)) {
                    $error = 'Service name is required.';
                } elseif ($price < 0) {
                    $error = 'Price must be a positive number.';
                } else {
                    try {
                        $stmt = $pdo->prepare("
                            INSERT INTO services (name, description, price, image, status)
                            VALUES (?, ?, ?, ?, ?)
                        ");
                        
                        if ($stmt->execute([$name, $description, $price, $image, $status])) {
                            $success = 'Service added successfully!';
                        } else {
                            $error = 'Failed to save service.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'edit':
                $serviceId = intval($_POST['service_id'] ?? 0);
                $name = trim($_POST['name'] ?? '');
                $description = trim($_POST['description'] ?? '');
                $price = floatval($_POST['price'] ?? 0);
                $image = trim($_POST['image'] ?? '');
                $status = isset($_POST['active']) ? 'active' : 'inactive';

                if (empty($name)) {
                    $error = 'Service name is required.';
                } elseif ($price < 0) {
                    $error = 'Price must be a positive number.';
                } else {
                    try {
                        $stmt = $pdo->prepare("
                            UPDATE services 
                            SET name = ?, description = ?, price = ?, image = ?, status = ?, updated_at = NOW()
                            WHERE id = ?
                        ");
                        
                        if ($stmt->execute([$name, $description, $price, $image, $status, $serviceId])) {
                            $success = 'Service updated successfully!';
                        } else {
                            $error = 'Failed to update service.';
                        }
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;

            case 'delete':
                $serviceId = intval($_POST['service_id'] ?? 0);
                
                try {
                    $stmt = $pdo->prepare("DELETE FROM services WHERE id = ?");
                    
                    if ($stmt->execute([$serviceId])) {
                        $success = 'Service deleted successfully!';
                    } else {
                        $error = 'Failed to delete service.';
                    }
                } catch (Exception $e) {
                    $error = 'Database error: ' . $e->getMessage();
                }
                break;

            case 'bulk_action':
                $selectedServices = $_POST['selected_services'] ?? [];
                $bulkAction = $_POST['bulk_action'] ?? '';

                if (empty($selectedServices) || empty($bulkAction)) {
                    $error = 'Please select services and an action.';
                } else {
                    $updatedCount = 0;
                    
                    try {
                        foreach ($selectedServices as $serviceId) {
                            switch ($bulkAction) {
                                case 'activate':
                                    $stmt = $pdo->prepare("UPDATE services SET status = 'active', updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($serviceId)])) $updatedCount++;
                                    break;
                                case 'deactivate':
                                    $stmt = $pdo->prepare("UPDATE services SET status = 'inactive', updated_at = NOW() WHERE id = ?");
                                    if ($stmt->execute([intval($serviceId)])) $updatedCount++;
                                    break;
                                case 'delete':
                                    $stmt = $pdo->prepare("DELETE FROM services WHERE id = ?");
                                    if ($stmt->execute([intval($serviceId)])) $updatedCount++;
                                    break;
                            }
                        }
                        
                        $success = "Bulk action completed successfully! {$updatedCount} services updated.";
                    } catch (Exception $e) {
                        $error = 'Database error: ' . $e->getMessage();
                    }
                }
                break;
        }
    }
}

// Get services from database
try {
    $stmt = $pdo->query("SELECT * FROM services ORDER BY created_at DESC");
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $services = [];
    $error = 'Failed to load services: ' . $e->getMessage();
}

// Apply filters
$filter = $_GET['filter'] ?? 'all';
$search = trim($_GET['search'] ?? '');

$filteredServices = $services;

// Apply search filter
if (!empty($search)) {
    $filteredServices = array_filter($filteredServices, function($service) use ($search) {
        return stripos($service['name'], $search) !== false ||
               stripos($service['description'], $search) !== false;
    });
}

// Apply status filter
switch ($filter) {
    case 'active':
        $filteredServices = array_filter($filteredServices, function($service) {
            return $service['status'] === 'active';
        });
        break;
    case 'inactive':
        $filteredServices = array_filter($filteredServices, function($service) {
            return $service['status'] === 'inactive';
        });
        break;
}

$pageTitle = 'Services Management - Admin';
$bodyClass = 'admin-services';

include BASE_PATH . 'includes/header.php';
?>

<style>
/* Enhanced dark mode text readability for services page */
.text-white-75 {
    color: rgba(255, 255, 255, 0.75) !important;
}

.text-white-60 {
    color: rgba(255, 255, 255, 0.6) !important;
}

.text-off-white {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Better form styling for dark mode */
.form-control, .form-select {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
    color: #ffffff !important;
}

.form-control:focus, .form-select:focus {
    background-color: #404040 !important;
    border-color: #FF00FF !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 0, 255, 0.25) !important;
}

.form-control::placeholder {
    color: rgba(255, 255, 255, 0.6) !important;
}

/* Form labels and text */
.form-label {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
}

.form-check-label {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Input group improvements */
.input-group-text {
    background-color: #404040 !important;
    border-color: #404040 !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Button improvements */
.btn-outline-cyan {
    color: #00FFFF !important;
    border-color: #00FFFF !important;
}

.btn-outline-cyan:hover {
    color: #000000 !important;
    background-color: #00FFFF !important;
    border-color: #00FFFF !important;
}

.btn-outline-danger {
    color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.btn-outline-danger:hover {
    color: #ffffff !important;
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.btn-yellow {
    background-color: #FFFF00 !important;
    border-color: #FFFF00 !important;
    color: #000000 !important;
    font-weight: 600;
}

.btn-yellow:hover {
    background-color: #e6e600 !important;
    border-color: #e6e600 !important;
    color: #000000 !important;
}

/* Table improvements */
.table-dark {
    background-color: transparent !important;
}

.table-dark td {
    border-color: #404040 !important;
    color: rgba(255, 255, 255, 0.9) !important;
    vertical-align: middle;
}

.table-dark th {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600;
    border-color: #404040 !important;
    background-color: #2d2d2d !important;
}

.table-dark tbody tr:hover {
    background-color: rgba(255, 0, 255, 0.05) !important;
}

/* Service name improvements */
.table-dark h6 {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600;
    margin-bottom: 0;
}

/* Description text improvements */
.table-dark small {
    color: rgba(255, 255, 255, 0.7) !important;
    line-height: 1.4;
}

/* Price styling */
.text-cyan.fw-bold {
    color: #00FFFF !important;
    font-weight: 700 !important;
}

/* Badge improvements */
.badge.bg-success {
    background-color: #28a745 !important;
    color: #ffffff !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

/* Modal improvements */
.modal-content {
    background-color: #1a1a1a !important;
    border-color: #404040 !important;
}

.modal-header {
    border-bottom-color: #404040 !important;
}

.modal-footer {
    border-top-color: #404040 !important;
}

.modal-title {
    color: #FF00FF !important;
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Alert improvements */
.alert-success {
    background-color: rgba(25, 135, 84, 0.1) !important;
    border-color: rgba(25, 135, 84, 0.3) !important;
    color: rgba(25, 135, 84, 0.9) !important;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
    color: rgba(220, 53, 69, 0.9) !important;
}

/* Checkbox improvements */
.form-check-input:checked {
    background-color: #FF00FF;
    border-color: #FF00FF;
}

.form-check-input:focus {
    border-color: #FF00FF;
    box-shadow: 0 0 0 0.25rem rgba(255, 0, 255, 0.25);
}

/* Empty state improvements */
.fa-concierge-bell {
    color: rgba(255, 255, 255, 0.4) !important;
}

/* Service image placeholder */
.bg-dark-grey-3 {
    background-color: #404040 !important;
}

/* Card header improvements */
.card-header h5 {
    font-weight: 600;
}

/* Button group improvements */
.btn-group .btn {
    font-weight: 500;
}

.btn-sm {
    font-size: 0.8rem;
    padding: 0.25rem 0.75rem;
}

/* Bulk operations improvements */
#selectedCount {
    color: #00FFFF !important;
    font-weight: 600;
}

/* Icon improvements */
.text-cyan {
    color: #00FFFF !important;
}

.text-magenta {
    color: #FF00FF !important;
}

.text-yellow {
    color: #FFFF00 !important;
}

/* Service image styling */
.table img {
    border: 1px solid #404040;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .btn-toolbar {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-group {
        width: 100%;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Better spacing */
.service-actions {
    display: flex;
    gap: 0.5rem;
}

/* Improve text contrast in all cards */
.card .text-white {
    color: rgba(255, 255, 255, 0.95) !important;
}

.card h5.text-white {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600;
}

.card h6.text-white {
    color: rgba(255, 255, 255, 0.95) !important;
    font-weight: 600;
}

/* Filter form improvements */
.form-label {
    margin-bottom: 0.5rem;
}

/* Table cell content alignment */
.table td {
    vertical-align: middle;
}

/* Service description truncation */
.service-description {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Better button spacing in table */
.btn-group .btn {
    margin-right: 0;
}

/* Improve visibility of form elements */
.form-control:disabled,
.form-select:disabled {
    background-color: #1a1a1a !important;
    color: rgba(255, 255, 255, 0.5) !important;
    border-color: #333333 !important;
}
</style>

<!-- Include Unified Sidebar -->
<?php include __DIR__ . '/includes/sidebar.php'; ?>

<!-- Main Content with Sidebar Offset -->
<div class="main-content">
    <div class="container-fluid px-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom border-dark-grey-3">
                <h1 class="h2 text-white">Services Management</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-magenta" data-bs-toggle="modal" data-bs-target="#serviceModal">
                        <i class="fas fa-plus me-2"></i>Add Service
                    </button>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success bg-dark-grey-2 border-success text-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger bg-dark-grey-2 border-danger text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Filters and Search -->
            <div class="card bg-dark-grey-1 border-cyan mb-4">
                <div class="card-header bg-dark-grey-2 border-cyan">
                    <h5 class="mb-0 text-cyan">
                        <i class="fas fa-filter me-2"></i>Filter Services
                    </h5>
                </div>
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="searchServices" class="form-label text-white">Search</label>
                            <input type="text" class="form-control" id="searchServices" name="search"
                                   placeholder="Search services..." value="<?php echo htmlspecialchars($search); ?>">
                        </div>
                        <div class="col-md-4">
                            <label for="filterStatus" class="form-label text-white">Filter</label>
                            <select class="form-select" id="filterStatus" name="filter">
                                <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>All Services</option>
                                <option value="active" <?php echo $filter === 'active' ? 'selected' : ''; ?>>Active Only</option>
                                <option value="inactive" <?php echo $filter === 'inactive' ? 'selected' : ''; ?>>Inactive Only</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label text-white">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-cyan">
                                    <i class="fas fa-search"></i> Filter
                                </button>
                                <a href="/admin/services.php" class="btn btn-danger">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Bulk Operations -->
            <div class="card bg-dark-grey-1 border-yellow mb-4">
                <div class="card-header bg-dark-grey-2 border-yellow">
                    <h5 class="mb-0 text-yellow">
                        <i class="fas fa-tasks me-2"></i>Bulk Operations
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="bulkForm">
                        <input type="hidden" name="action" value="bulk_action">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                        <div class="row g-3 align-items-end">
                            <div class="col-md-4">
                                <label class="form-label text-white">Select Action</label>
                                <select class="form-select" name="bulk_action" required>
                                    <option value="">Choose action...</option>
                                    <option value="activate">Activate Selected</option>
                                    <option value="deactivate">Deactivate Selected</option>
                                    <option value="delete">Delete Selected</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <button type="button" class="btn btn-outline-cyan" onclick="selectAllServices()">
                                    <i class="fas fa-check-square me-2"></i>Select All
                                </button>
                                <button type="button" class="btn btn-danger" onclick="clearSelection()">
                                    <i class="fas fa-square me-2"></i>Clear All
                                </button>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="btn btn-yellow text-dark" onclick="return confirmBulkAction()">
                                    <i class="fas fa-play me-2"></i>Execute Action
                                </button>
                                <span class="text-off-white ms-2">
                                    <span id="selectedCount">0</span> selected
                                </span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Services List -->
            <div class="card bg-dark-grey-1 border-magenta">
                <div class="card-header bg-dark-grey-2 border-magenta">
                    <h5 class="mb-0 text-magenta">
                        <i class="fas fa-concierge-bell me-2"></i>
                        Services (<?php echo count($filteredServices); ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($filteredServices)): ?>
                        <div class="text-center py-5">
                            <i class="fas fa-concierge-bell fa-3x text-off-white mb-3"></i>
                            <h5 class="text-white">No services found</h5>
                            <p class="text-off-white">Add your first service to get started.</p>
                            <button type="button" class="btn btn-magenta" data-bs-toggle="modal" data-bs-target="#serviceModal">
                                <i class="fas fa-plus me-2"></i>Add Service
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-dark table-hover">
                                <thead>
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input" id="selectAll" onchange="toggleAllServices()">
                                        </th>
                                        <th>Service</th>
                                        <th>Description</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Created</th>
                                        <th width="120">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($filteredServices as $service): ?>
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input service-select"
                                                       name="selected_services[]" value="<?php echo $service['id']; ?>"
                                                       onchange="updateSelectedCount()">
                                            </td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($service['image'])): ?>
                                                        <img src="<?php echo SITE_URL; ?>/assets/images/services/<?php echo htmlspecialchars($service['image']); ?>"
                                                             alt="<?php echo htmlspecialchars($service['name']); ?>"
                                                             class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;"
                                                             onerror="this.src='<?php echo SITE_URL; ?>/assets/images/placeholder.jpg'">
                                                    <?php else: ?>
                                                        <div class="bg-dark-grey-3 rounded me-3 d-flex align-items-center justify-content-center"
                                                             style="width: 50px; height: 50px;">
                                                            <i class="fas fa-concierge-bell text-off-white"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <h6 class="mb-0 text-white"><?php echo htmlspecialchars($service['name']); ?></h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <small class="text-off-white">
                                                    <?php echo htmlspecialchars(substr($service['description'], 0, 100)); ?>
                                                    <?php if (strlen($service['description']) > 100): ?>...<?php endif; ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="text-cyan fw-bold">$<?php echo number_format($service['price'], 2); ?></span>
                                            </td>
                                            <td>
                                                <?php if ($service['status'] === 'active'): ?>
                                                    <span class="badge bg-success">Active</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger">Inactive</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <small class="text-off-white">
                                                    <?php echo date('M j, Y', strtotime($service['created_at'])); ?>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button class="btn btn-sm btn-outline-cyan"
                                                            onclick="editService(<?php echo $service['id']; ?>)"
                                                            title="Edit Service">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteService(<?php echo $service['id']; ?>)"
                                                            title="Delete Service">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
    </div>
</div>

<!-- Service Modal -->
<div class="modal fade" id="serviceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content bg-dark-grey-1 border-magenta">
            <div class="modal-header bg-dark-grey-2 border-magenta">
                <h5 class="modal-title text-magenta">
                    <i class="fas fa-concierge-bell me-2"></i>
                    <span id="modalTitle">Add Service</span>
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="serviceForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" id="formAction" value="add">
                    <input type="hidden" name="service_id" id="serviceId">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="serviceName" class="form-label text-white">Service Name *</label>
                                <input type="text" class="form-control" id="serviceName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="servicePrice" class="form-label text-white">Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="servicePrice" name="price"
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="serviceDescription" class="form-label text-white">Description</label>
                        <textarea class="form-control" id="serviceDescription" name="description" rows="4"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="serviceImage" class="form-label text-white">Service Image</label>
                        <input type="text" class="form-control" id="serviceImage" name="image"
                               placeholder="service-image.jpg">
                        <div class="form-text text-off-white">
                            Enter the filename of the image in the /assets/images/services/ directory
                        </div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="serviceActive" name="active" checked>
                        <label class="form-check-label text-white" for="serviceActive">
                            Active Service
                        </label>
                    </div>
                </div>
                <div class="modal-footer bg-dark-grey-2 border-magenta">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Save Service
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Service management functions
function editService(serviceId) {
    // Find service data
    const services = <?php echo json_encode($services); ?>;
    const service = services.find(s => s.id == serviceId);

    if (service) {
        document.getElementById('modalTitle').textContent = 'Edit Service';
        document.getElementById('formAction').value = 'edit';
        document.getElementById('serviceId').value = service.id;
        document.getElementById('serviceName').value = service.name || '';
        document.getElementById('serviceDescription').value = service.description || '';
        document.getElementById('servicePrice').value = service.price || '';
        document.getElementById('serviceImage').value = service.image || '';
        document.getElementById('serviceActive').checked = service.status === 'active';

        new bootstrap.Modal(document.getElementById('serviceModal')).show();
    }
}

function deleteService(serviceId) {
    if (confirm('Are you sure you want to delete this service? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete">
            <input type="hidden" name="service_id" value="${serviceId}">
            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// Reset form when modal is hidden
document.getElementById('serviceModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('serviceForm').reset();
    document.getElementById('modalTitle').textContent = 'Add Service';
    document.getElementById('formAction').value = 'add';
    document.getElementById('serviceId').value = '';
});

// Bulk operations functions
function selectAllServices() {
    const checkboxes = document.querySelectorAll('.service-select');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function clearSelection() {
    const checkboxes = document.querySelectorAll('.service-select');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

function toggleAllServices() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.service-select');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.service-select:checked');
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;

    // Update select all checkbox
    const selectAll = document.getElementById('selectAll');
    const allCheckboxes = document.querySelectorAll('.service-select');
    if (selectAll) {
        selectAll.checked = selectedCheckboxes.length === allCheckboxes.length && allCheckboxes.length > 0;
    }
}

function confirmBulkAction() {
    const selectedCheckboxes = document.querySelectorAll('.service-select:checked');
    const action = document.querySelector('select[name="bulk_action"]').value;

    if (selectedCheckboxes.length === 0) {
        alert('Please select at least one service.');
        return false;
    }

    if (!action) {
        alert('Please select an action.');
        return false;
    }

    const actionText = action === 'delete' ? 'delete' : action;
    const confirmMessage = `Are you sure you want to ${actionText} ${selectedCheckboxes.length} selected services?`;

    if (action === 'delete') {
        return confirm(confirmMessage + ' This action cannot be undone.');
    }

    return confirm(confirmMessage);
}

// Add selected services to bulk form before submission
document.getElementById('bulkForm').addEventListener('submit', function(e) {
    const selectedCheckboxes = document.querySelectorAll('.service-select:checked');

    // Remove existing hidden inputs
    const existingInputs = this.querySelectorAll('input[name="selected_services[]"]');
    existingInputs.forEach(input => input.remove());

    // Add selected services as hidden inputs
    selectedCheckboxes.forEach(checkbox => {
        const hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.name = 'selected_services[]';
        hiddenInput.value = checkbox.value;
        this.appendChild(hiddenInput);
    });
});
</script>


